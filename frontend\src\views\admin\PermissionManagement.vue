<template>
  <div class="permission-management">
    <!-- 权限检查：无权限时显示提示 -->
    <div v-if="!hasPermissionAccess" class="no-permission-container">
      <div class="no-permission-content">
        <div class="no-permission-icon">
          <el-icon :size="80">
            <Lock />
          </el-icon>
        </div>
        <h2 class="no-permission-title">访问受限</h2>
        <p class="no-permission-message">您没有权限访问权限管理功能</p>
        <div class="no-permission-description">
          <p>权限管理功能仅限管理员使用。</p>
          <p>如需访问，请联系系统管理员为您分配相应权限。</p>
        </div>
        <div class="no-permission-actions">
          <el-button type="primary" @click="goBack">返回上一页</el-button>
          <el-button @click="goHome">回到首页</el-button>
        </div>
      </div>
    </div>

    <!-- 有权限时显示正常内容 -->
    <div v-else>
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">权限管理</h1>
        <p class="page-subtitle">管理用户、系统配置和权限分配</p>
      </div>

      <!-- 功能模块选项卡 -->
      <el-tabs v-model="activeTab" class="permission-tabs">
        <!-- 用户权限分配模块 -->
        <el-tab-pane label="用户权限分配" name="user-permissions">
          <UserPermissionAssignment />
        </el-tab-pane>

        <!-- 用户管理模块 -->
        <el-tab-pane label="用户管理" name="users">
          <div class="module-container">
            <div class="module-header">
              <h2>用户管理</h2>
              <el-button type="primary" @click="showCreateUserDialog = true">
                <el-icon><Plus /></el-icon>
                创建用户
              </el-button>
            </div>

            <!-- 用户列表 -->
            <el-table :data="users" class="user-table" v-loading="loadingUsers">
              <el-table-column prop="username" label="用户名" width="200" />
              <el-table-column prop="role" label="角色" width="120">
                <template #default="{ row }">
                  <el-tag :type="getRoleTagType(row.role)">
                    {{ getRoleDisplayName(row.role) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="created_at" label="创建时间" width="180">
                <template #default="{ row }">
                  {{ formatDate(row.created_at) }}
                </template>
              </el-table-column>
              <el-table-column prop="is_active" label="状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="row.is_active ? 'success' : 'danger'">
                    {{ row.is_active ? '活跃' : '禁用' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200">
                <template #default="{ row }">
                  <el-button
                    type="primary"
                    size="small"
                    @click="handleChangePassword(row)"
                  >
                    修改密码
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    @click="handleDeleteUser(row)"
                    :disabled="row.username === 'admin'"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <!-- 系统配置管理模块 -->
        <el-tab-pane label="系统配置" name="systems">
          <div class="module-container">
            <div class="module-header">
              <h2>系统配置管理</h2>
              <el-button type="primary" @click="showCreateSystemDialog = true">
                <el-icon><Plus /></el-icon>
                创建系统
              </el-button>
            </div>

            <!-- 系统列表 -->
            <div v-if="systems.length === 0" class="no-systems">
              <el-empty description="暂无系统配置数据，系统信息将从用户权限中自动提取" />
            </div>
            <div v-else class="systems-grid">
              <div v-for="system in systems" :key="system.id" class="system-card">
                <el-card shadow="hover">
                  <template #header>
                    <div class="card-header">
                      <div class="system-info">
                        <span class="system-name">{{ system.name }}</span>
                      </div>
                      <div class="header-actions">
                        <el-button
                          type="info"
                          size="small"
                          @click="showEditSystemDialogHandler(system)"
                        >
                          <el-icon><Edit /></el-icon>
                          编辑
                        </el-button>
                        <el-button
                          type="primary"
                          size="small"
                          @click="showAddConfigDialogHandler(system)"
                        >
                          <el-icon><Plus /></el-icon>
                          添加配置项
                        </el-button>

                      </div>
                    </div>
                  </template>

                  <!-- 配置项树形结构 -->
                  <el-tree
                    :data="system.configs"
                    :props="{ label: 'name', children: 'children' }"
                    node-key="id"
                    class="config-tree"
                  >
                    <template #default="{ node, data }">
                      <div class="tree-node">
                        <span>{{ data.name }}</span>
                        <div class="node-actions">
                          <el-button
                            type="text"
                            size="small"
                            @click="handleEditConfig(system, data)"
                          >
                            <el-icon><Edit /></el-icon>
                          </el-button>
                          <el-button
                            type="text"
                            size="small"
                            @click="handleDeleteConfig(system, data)"
                          >
                            <el-icon><Delete /></el-icon>
                          </el-button>
                        </div>
                      </div>
                    </template>
                  </el-tree>
                </el-card>
              </div>
            </div>
          </div>
        </el-tab-pane>


      </el-tabs>

      <!-- 修改密码对话框 -->
      <el-dialog v-model="showChangePasswordDialog" title="修改用户密码" width="400px">
        <el-form
          ref="changePasswordFormRef"
          :model="changePasswordForm"
          :rules="changePasswordRules"
          label-width="100px"
        >
          <el-form-item label="用户名">
            <el-input v-model="changePasswordForm.username" disabled />
          </el-form-item>
          <el-form-item label="新密码" prop="newPassword">
            <el-input
              v-model="changePasswordForm.newPassword"
              type="password"
              placeholder="请输入新密码"
              show-password
            />
          </el-form-item>
          <el-form-item label="管理员密码" prop="adminPassword">
            <el-input
              v-model="changePasswordForm.adminPassword"
              type="password"
              placeholder="请输入管理员密码"
              show-password
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="handleCancelChangePassword">取消</el-button>
            <el-button type="primary" @click="handleConfirmChangePassword" :loading="changingPassword">
              确定
            </el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 创建用户对话框 -->
      <el-dialog v-model="showCreateUserDialog" title="创建用户" width="500px">
        <el-form
          ref="createUserFormRef"
          :model="createUserForm"
          :rules="createUserRules"
          label-width="100px"
        >
          <el-form-item label="用户名" prop="username">
            <el-input v-model="createUserForm.username" placeholder="请输入用户名" />
          </el-form-item>
          <el-form-item label="密码" prop="password">
            <el-input
              v-model="createUserForm.password"
              type="password"
              placeholder="请输入密码"
              show-password
            />
          </el-form-item>
          <el-form-item label="角色" prop="role">
            <el-select v-model="createUserForm.role" placeholder="请选择角色">
              <el-option label="管理员" value="admin" />
              <el-option label="普通用户" value="user" />
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="showCreateUserDialog = false">取消</el-button>
          <el-button type="primary" @click="handleCreateUser" :loading="creatingUser">
            创建
          </el-button>
        </template>
      </el-dialog>

      <!-- 创建系统对话框 -->
      <el-dialog v-model="showCreateSystemDialog" title="创建系统" width="600px">
        <el-form
          ref="createSystemFormRef"
          :model="createSystemForm"
          :rules="createSystemRules"
          label-width="120px"
        >
          <el-form-item label="系统名称" prop="systemName">
            <el-input v-model="createSystemForm.systemName" placeholder="请输入系统名称" />
          </el-form-item>

          <el-form-item label="所属子系统" prop="subsystem">
            <el-input v-model="createSystemForm.subsystem" placeholder="请输入所属子系统" />
          </el-form-item>

          <el-form-item label="软件名称" prop="softwareName">
            <el-input v-model="createSystemForm.softwareName" placeholder="请输入软件名称" />
          </el-form-item>

          <el-form-item label="软件类型" prop="softwareType">
            <el-input v-model="createSystemForm.softwareType" placeholder="请输入软件类型" />
          </el-form-item>

          <el-form-item label="安全等级" prop="securityLevel">
            <el-input v-model="createSystemForm.securityLevel" placeholder="请输入安全等级" />
          </el-form-item>

          <el-form-item label="运行环境" prop="runtimeEnv">
            <el-input v-model="createSystemForm.runtimeEnv" placeholder="请输入运行环境" />
          </el-form-item>

          <el-form-item label="开发环境" prop="developEnv">
            <el-input v-model="createSystemForm.developEnv" placeholder="请输入开发环境" />
          </el-form-item>

          <el-form-item label="编程语言" prop="language">
            <el-input v-model="createSystemForm.language" placeholder="请输入编程语言" />
          </el-form-item>

          <el-form-item label="版本" prop="version">
            <el-input v-model="createSystemForm.version" placeholder="请输入版本号" />
          </el-form-item>

          <el-form-item label="代码模板" prop="codeTemplate">
            <el-input v-model="createSystemForm.codeTemplate" placeholder="请输入代码模板" />
          </el-form-item>

          <el-form-item label="研制单位" prop="developer">
            <el-input v-model="createSystemForm.developer" placeholder="请输入研制单位" />
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="showCreateSystemDialog = false">取消</el-button>
          <el-button type="primary" @click="handleCreateSystem" :loading="creatingSystem">
            创建
          </el-button>
        </template>
      </el-dialog>

      <!-- 编辑系统对话框 -->
      <el-dialog v-model="showEditSystemDialog" title="编辑系统" width="600px">
        <el-form
          ref="editSystemFormRef"
          :model="editSystemForm"
          :rules="editSystemRules"
          label-width="120px"
        >
          <el-form-item label="系统名称">
            <el-input v-model="editSystemForm.systemName" disabled />
          </el-form-item>

          <el-form-item label="所属子系统" prop="subsystem">
            <el-input v-model="editSystemForm.subsystem" placeholder="请输入所属子系统" />
          </el-form-item>

          <el-form-item label="软件名称" prop="softwareName">
            <el-input v-model="editSystemForm.softwareName" placeholder="请输入软件名称" />
          </el-form-item>

          <el-form-item label="软件类型" prop="softwareType">
            <el-input v-model="editSystemForm.softwareType" placeholder="请输入软件类型" />
          </el-form-item>

          <el-form-item label="安全等级" prop="securityLevel">
            <el-input v-model="editSystemForm.securityLevel" placeholder="请输入安全等级" />
          </el-form-item>

          <el-form-item label="运行环境" prop="runtimeEnv">
            <el-input v-model="editSystemForm.runtimeEnv" placeholder="请输入运行环境" />
          </el-form-item>

          <el-form-item label="开发环境" prop="developEnv">
            <el-input v-model="editSystemForm.developEnv" placeholder="请输入开发环境" />
          </el-form-item>

          <el-form-item label="编程语言" prop="language">
            <el-input v-model="editSystemForm.language" placeholder="请输入编程语言" />
          </el-form-item>

          <el-form-item label="版本" prop="version">
            <el-input v-model="editSystemForm.version" placeholder="请输入版本号" />
          </el-form-item>

          <el-form-item label="代码模板" prop="codeTemplate">
            <el-input v-model="editSystemForm.codeTemplate" placeholder="请输入代码模板" />
          </el-form-item>

          <el-form-item label="研制单位" prop="developer">
            <el-input v-model="editSystemForm.developer" placeholder="请输入研制单位" />
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="showEditSystemDialog = false">取消</el-button>
          <el-button type="primary" @click="handleUpdateSystem" :loading="updatingSystem">
            保存
          </el-button>
        </template>
      </el-dialog>

      <!-- 添加配置项对话框 -->
      <el-dialog v-model="showAddConfigDialog" title="添加配置项" width="600px">
        <el-form
          ref="addConfigFormRef"
          :model="addConfigForm"
          :rules="addConfigRules"
          label-width="120px"
        >
          <el-form-item label="配置项名称" prop="configItemName">
            <el-input v-model="addConfigForm.configItemName" placeholder="请输入配置项名称" />
          </el-form-item>

          <el-form-item label="所属子系统" prop="subsystem">
            <el-input v-model="addConfigForm.subsystem" placeholder="请输入所属子系统" />
          </el-form-item>

          <el-form-item label="软件名称" prop="softwareName">
            <el-input v-model="addConfigForm.softwareName" placeholder="请输入软件名称" />
          </el-form-item>

          <el-form-item label="软件类型" prop="softwareType">
            <el-input v-model="addConfigForm.softwareType" placeholder="请输入软件类型" />
          </el-form-item>

          <el-form-item label="安全等级" prop="securityLevel">
            <el-input v-model="addConfigForm.securityLevel" placeholder="请输入安全等级" />
          </el-form-item>

          <el-form-item label="运行环境" prop="runtimeEnv">
            <el-input v-model="addConfigForm.runtimeEnv" placeholder="请输入运行环境" />
          </el-form-item>

          <el-form-item label="开发环境" prop="developEnv">
            <el-input v-model="addConfigForm.developEnv" placeholder="请输入开发环境" />
          </el-form-item>

          <el-form-item label="编程语言" prop="language">
            <el-input v-model="addConfigForm.language" placeholder="请输入编程语言" />
          </el-form-item>

          <el-form-item label="版本" prop="version">
            <el-input v-model="addConfigForm.version" placeholder="请输入版本" />
          </el-form-item>

          <el-form-item label="代码模板" prop="codeTemplate">
            <el-input v-model="addConfigForm.codeTemplate" placeholder="请输入代码模板" />
          </el-form-item>

          <el-form-item label="研制单位" prop="developer">
            <el-input v-model="addConfigForm.developer" placeholder="请输入研制单位" />
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="showAddConfigDialog = false">取消</el-button>
          <el-button type="primary" @click="handleAddConfig" :loading="addingConfig">
            添加
          </el-button>
        </template>
      </el-dialog>

      <!-- 编辑配置项对话框 -->
      <el-dialog v-model="showEditConfigDialog" title="编辑配置项" width="600px">
        <el-form
          ref="editConfigFormRef"
          :model="editConfigForm"
          :rules="editConfigRules"
          label-width="120px"
        >
          <el-form-item label="配置项名称" prop="newConfigItemName">
            <el-input v-model="editConfigForm.newConfigItemName" placeholder="请输入新的配置项名称" />
          </el-form-item>

          <el-form-item label="所属子系统" prop="subsystem">
            <el-input v-model="editConfigForm.subsystem" placeholder="请输入所属子系统" />
          </el-form-item>

          <el-form-item label="软件名称" prop="softwareName">
            <el-input v-model="editConfigForm.softwareName" placeholder="请输入软件名称" />
          </el-form-item>

          <el-form-item label="软件类型" prop="softwareType">
            <el-input v-model="editConfigForm.softwareType" placeholder="请输入软件类型" />
          </el-form-item>

          <el-form-item label="安全等级" prop="securityLevel">
            <el-input v-model="editConfigForm.securityLevel" placeholder="请输入安全等级" />
          </el-form-item>

          <el-form-item label="运行环境" prop="runtimeEnv">
            <el-input v-model="editConfigForm.runtimeEnv" placeholder="请输入运行环境" />
          </el-form-item>

          <el-form-item label="开发环境" prop="developEnv">
            <el-input v-model="editConfigForm.developEnv" placeholder="请输入开发环境" />
          </el-form-item>

          <el-form-item label="编程语言" prop="language">
            <el-input v-model="editConfigForm.language" placeholder="请输入编程语言" />
          </el-form-item>

          <el-form-item label="版本" prop="version">
            <el-input v-model="editConfigForm.version" placeholder="请输入版本" />
          </el-form-item>

          <el-form-item label="代码模板" prop="codeTemplate">
            <el-input v-model="editConfigForm.codeTemplate" placeholder="请输入代码模板" />
          </el-form-item>

          <el-form-item label="研制单位" prop="developer">
            <el-input v-model="editConfigForm.developer" placeholder="请输入研制单位" />
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="showEditConfigDialog = false">取消</el-button>
          <el-button type="primary" @click="handleUpdateConfig" :loading="updatingConfig">
            保存
          </el-button>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Delete, Lock, Edit } from '@element-plus/icons-vue'
import { apiService, type UserData } from '@/services/api'
import type { CreateUserRequest } from '@/types/api/user'
import { usePermission } from '@/composables/usePermission'
import { Permission } from '@/types/api/user'
import UserPermissionAssignment from './UserPermissionAssignment.vue'

// 权限检查
const { hasPermission } = usePermission()
const router = useRouter()

// 检查用户是否有权限管理权限
const hasPermissionAccess = computed(() => {
  return hasPermission(Permission.PERMISSION_MANAGEMENT)
})

// 导航方法
const goBack = () => {
  router.go(-1)
}

const goHome = () => {
  router.push('/app/dashboard')
}

// 页面标题设置
document.title = '权限管理 - 用例管理平台'

// 响应式数据
const activeTab = ref('user-permissions')
const loadingUsers = ref(false)
const creatingUser = ref(false)
const creatingSystem = ref(false)
const updatingSystem = ref(false)
const addingConfig = ref(false)
const updatingConfig = ref(false)

// 对话框显示状态
const showCreateUserDialog = ref(false)
const showChangePasswordDialog = ref(false)
const showCreateSystemDialog = ref(false)
const showEditSystemDialog = ref(false)
const showAddConfigDialog = ref(false)
const showEditConfigDialog = ref(false)

// 表单引用
const createUserFormRef = ref<FormInstance>()
const changePasswordFormRef = ref<FormInstance>()
const createSystemFormRef = ref<FormInstance>()
const editSystemFormRef = ref<FormInstance>()
const addConfigFormRef = ref<FormInstance>()
const editConfigFormRef = ref<FormInstance>()

// 数据
const users = ref<UserData[]>([])
const systems = ref<any[]>([])
const currentSystem = ref<any>(null)

// 表单数据
const createUserForm = reactive<CreateUserRequest>({
  username: '',
  password: '',
  role: 'user',
})

const changePasswordForm = reactive({
  userId: '',
  username: '',
  newPassword: '',
  adminPassword: ''
})

const changingPassword = ref(false)

const createSystemForm = reactive({
  systemName: '',
  subsystem: '',
  softwareName: '',
  softwareType: '',
  securityLevel: '',
  runtimeEnv: '',
  developEnv: '',
  language: '',
  version: '',
  codeTemplate: '',
  developer: '',
})

const editSystemForm = reactive({
  systemName: '',
  subsystem: '',
  softwareName: '',
  softwareType: '',
  securityLevel: '',
  runtimeEnv: '',
  developEnv: '',
  language: '',
  version: '',
  codeTemplate: '',
  developer: '',
})

const addConfigForm = reactive({
  configItemName: '',
  subsystem: '',
  softwareName: '',
  softwareType: '',
  securityLevel: '',
  runtimeEnv: '',
  developEnv: '',
  language: '',
  version: '',
  codeTemplate: '',
  developer: '',
})

const editConfigForm = reactive({
  systemName: '',
  configItemName: '',
  newConfigItemName: '',
  subsystem: '',
  softwareName: '',
  softwareType: '',
  securityLevel: '',
  runtimeEnv: '',
  developEnv: '',
  language: '',
  version: '',
  codeTemplate: '',
  developer: '',
})

// 表单验证规则
const createUserRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 7, message: '密码长度不能少于7个字符', trigger: 'blur' },
  ],
  role: [{ required: true, message: '请选择角色', trigger: 'change' }],
}

const changePasswordRules: FormRules = {
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' },
  ],
  adminPassword: [
    { required: true, message: '请输入管理员密码', trigger: 'blur' },
  ],
}

const createSystemRules: FormRules = {
  systemName: [
    { required: true, message: '请输入系统名称', trigger: 'blur' },
    { min: 2, max: 50, message: '系统名称长度在2到50个字符', trigger: 'blur' },
  ],
  subsystem: [
    { required: true, message: '请输入所属子系统', trigger: 'blur' },
  ],
  softwareName: [
    { required: true, message: '请输入软件名称', trigger: 'blur' },
  ],
  softwareType: [
    { required: true, message: '请输入软件类型', trigger: 'blur' },
  ],
  securityLevel: [
    { required: true, message: '请输入安全等级', trigger: 'blur' },
  ],
  runtimeEnv: [
    { required: true, message: '请输入运行环境', trigger: 'blur' },
  ],
  developEnv: [
    { required: true, message: '请输入开发环境', trigger: 'blur' },
  ],
  language: [
    { required: true, message: '请输入编程语言', trigger: 'blur' },
  ],
  version: [
    { required: true, message: '请输入版本号', trigger: 'blur' },
  ],
  codeTemplate: [
    { required: true, message: '请输入代码模板', trigger: 'blur' },
  ],
  developer: [
    { required: true, message: '请输入研制单位', trigger: 'blur' },
  ],
}

const editSystemRules: FormRules = {
  subsystem: [
    { required: true, message: '请输入所属子系统', trigger: 'blur' },
  ],
  softwareName: [
    { required: true, message: '请输入软件名称', trigger: 'blur' },
  ],
  softwareType: [
    { required: true, message: '请输入软件类型', trigger: 'blur' },
  ],
  securityLevel: [
    { required: true, message: '请输入安全等级', trigger: 'blur' },
  ],
  runtimeEnv: [
    { required: true, message: '请输入运行环境', trigger: 'blur' },
  ],
  developEnv: [
    { required: true, message: '请输入开发环境', trigger: 'blur' },
  ],
  language: [
    { required: true, message: '请输入编程语言', trigger: 'blur' },
  ],
  version: [
    { required: true, message: '请输入版本号', trigger: 'blur' },
  ],
  codeTemplate: [
    { required: true, message: '请输入代码模板', trigger: 'blur' },
  ],
  developer: [
    { required: true, message: '请输入研制单位', trigger: 'blur' },
  ],
}

const addConfigRules: FormRules = {
  configItemName: [
    { required: true, message: '请输入配置项名称', trigger: 'blur' },
    { min: 2, max: 50, message: '配置项名称长度在2到50个字符', trigger: 'blur' },
  ],
}

const editConfigRules: FormRules = {
  newConfigItemName: [
    { required: true, message: '请输入新的配置项名称', trigger: 'blur' },
    { min: 2, max: 50, message: '配置项名称长度在2到50个字符', trigger: 'blur' },
  ],
}

// 工具方法
const getRoleTagType = (role: string) => {
  switch (role) {
    case 'admin':
      return 'danger'
    case 'user':
      return 'success'
    default:
      return 'info'
  }
}

const getRoleDisplayName = (role: string) => {
  switch (role) {
    case 'admin':
      return '管理员'
    case 'user':
      return '普通用户'
    default:
      return '未知角色'
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 用户管理方法
const loadUsers = async () => {
  try {
    loadingUsers.value = true
    console.log('🔄 开始加载用户列表...')

    const response = await apiService.permission.getUsers()
    console.log('📦 用户列表API响应:', response)

    // 处理API响应数据
    if (response && response.success && response.data && response.data.users) {
      users.value = response.data.users
      console.log('✅ 用户列表加载成功:', users.value.length, '个用户')
    } else if (response && response.data && Array.isArray(response.data)) {
      users.value = response.data
      console.log('✅ 用户列表加载成功（直接数组）:', users.value.length, '个用户')
    } else {
      console.warn('⚠️ 用户列表响应格式异常:', response)
      users.value = []
    }
  } catch (error: any) {
    console.error('❌ 加载用户列表失败:', error)
    ElMessage.error('加载用户列表失败：' + (error.message || '未知错误'))
    users.value = []
  } finally {
    loadingUsers.value = false
  }
}

const handleCreateUser = async () => {
  if (!createUserFormRef.value) return

  try {
    await createUserFormRef.value.validate()
    creatingUser.value = true

    await apiService.permission.createUser(createUserForm)
    ElMessage.success('用户创建成功')

    showCreateUserDialog.value = false
    resetCreateUserForm()
    await loadUsers()
  } catch (error: any) {
    ElMessage.error('创建用户失败：' + (error.message || '未知错误'))
  } finally {
    creatingUser.value = false
  }
}

// 修改密码相关函数
const handleChangePassword = (user: UserData) => {
  changePasswordForm.userId = user._id
  changePasswordForm.username = user.username
  changePasswordForm.newPassword = ''
  changePasswordForm.adminPassword = ''
  showChangePasswordDialog.value = true
}

const handleCancelChangePassword = () => {
  showChangePasswordDialog.value = false
  changePasswordForm.userId = ''
  changePasswordForm.username = ''
  changePasswordForm.newPassword = ''
  changePasswordForm.adminPassword = ''
}

const handleConfirmChangePassword = async () => {
  if (!changePasswordFormRef.value) return

  try {
    await changePasswordFormRef.value.validate()
    changingPassword.value = true

    const response = await apiService.permission.changePassword(changePasswordForm.userId, {
      new_password: changePasswordForm.newPassword,
      admin_password: changePasswordForm.adminPassword
    })

    if (response.success) {
      ElMessage.success(response.message || '密码修改成功')
      showChangePasswordDialog.value = false
      handleCancelChangePassword()
    } else {
      ElMessage.error(response.message || '密码修改失败')
    }
  } catch (error: any) {
    ElMessage.error('密码修改失败：' + (error.message || '未知错误'))
  } finally {
    changingPassword.value = false
  }
}

const handleDeleteUser = async (user: UserData) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${user.username}" 吗？此操作不可恢复。\n被删除用户的所有活跃会话将被强制退出。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    // 1. 调用API删除用户
    await apiService.permission.deleteUser(user._id)

    // 2. 导入并使用会话管理器清理被删除用户的会话
    const { userSessionManager } = await import('@/services/userSessionManager')
    await userSessionManager.handleUserDeleted(user.username)

    ElMessage.success(`用户 ${user.username} 删除成功，相关会话已清理`)
    await loadUsers()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('删除用户失败：' + (error.message || '未知错误'))
    }
  }
}

const resetCreateUserForm = () => {
  createUserForm.username = ''
  createUserForm.password = ''
  createUserForm.role = 'user' as 'admin' | 'user'
  createUserFormRef.value?.clearValidate()
}

// 系统配置管理方法
const loadSystems = async (): Promise<void> => {
  try {
    console.log('🔄 开始加载系统配置管理数据...');

    // 使用正确的用例管理系统接口
    const systemsData = await apiService.caseManagement.getSystems();
    console.log('📦 系统配置数据响应:', systemsData);

    // 解析API响应数据 - 正确提取data字段
    const systemsList: any[] = [];

    console.log('🔍 解析系统数据:', systemsData);

    // 提取实际的系统数据
    let actualSystemsData = systemsData;
    if (systemsData && systemsData.data) {
      actualSystemsData = systemsData.data;
      console.log('📊 提取到的实际系统数据:', actualSystemsData);
    }

    // 检查数据格式并处理
    if (actualSystemsData && typeof actualSystemsData === 'object') {
      // 遍历系统数据中的每个系统
      Object.entries(actualSystemsData).forEach(([systemKey, systemData]: [string, any]) => {
      console.log(`🏢 处理系统 ${systemKey}:`, systemData);

      // 安全检查系统数据结构
      if (!systemData || typeof systemData !== 'object') {
        console.warn(`⚠️ 系统 ${systemKey} 数据无效，跳过处理`);
        return;
      }

      console.log(`🔍 系统数据结构检查:`, {
        hasData: !!(systemData.data),
        dataFields: systemData.data && typeof systemData.data === 'object' ? Object.keys(systemData.data) : [],
        directFields: Object.keys(systemData)
      });

      // 获取系统的详细信息，优先从data字段获取，然后从直接属性获取
      const systemInfo = systemData.data || systemData;

      const system = {
        id: systemKey,
        name: getSystemDisplayName(systemKey),
        subsystem: systemInfo.subsystem || systemData.subsystem || '',
        softwareName: systemInfo.softwareName || systemData.softwareName || '',
        softwareType: systemInfo.softwareType || systemData.softwareType || '',
        securityLevel: systemInfo.securityLevel || systemData.securityLevel || '',
        runtimeEnv: systemInfo.runtimeEnv || systemData.runtimeEnv || '',
        developEnv: systemInfo.developEnv || systemData.developEnv || '',
        language: systemInfo.language || systemData.language || '',
        version: systemInfo.version || systemData.version || '',
        codeTemplate: systemInfo.codeTemplate || systemData.codeTemplate || '',
        developer: systemInfo.developer || systemData.developer || '',
        configs: [] as any[]
      };

      // 解析配置项 - 从path数组中提取
      if (systemData.path && Array.isArray(systemData.path)) {
        console.log(`  📋 处理配置项路径:`, systemData.path);

        systemData.path.forEach((pathItem: any, pathIndex: number) => {
          if (pathItem && typeof pathItem === 'object') {
            Object.keys(pathItem).forEach(configName => {
              const configData = pathItem[configName];
              console.log(`    ➕ 添加配置项: ${configName}`, configData);
              console.log(`    🔍 配置项数据结构检查:`, {
                hasData: !!configData.data,
                dataFields: configData.data ? Object.keys(configData.data) : [],
                directFields: Object.keys(configData)
              });

              // 获取配置项的详细信息，优先从data字段获取，然后从直接属性获取
              const configInfo = configData.data || configData;

              system.configs.push({
                id: `${systemKey}_${configName}`,
                name: configName,
                description: `${configName} 配置项`,
                data: configData,
                pathIndex: pathIndex,
                // 添加配置项的所有字段信息
                subsystem: configInfo.subsystem || '',
                softwareName: configInfo.softwareName || '',
                softwareType: configInfo.softwareType || '',
                securityLevel: configInfo.securityLevel || '',
                runtimeEnv: configInfo.runtimeEnv || '',
                developEnv: configInfo.developEnv || '',
                language: configInfo.language || '',
                version: configInfo.version || '',
                codeTemplate: configInfo.codeTemplate || '',
                developer: configInfo.developer || '',
                tableName: configData.tableName || ''
              });
            });
          }
        });
      }

        systemsList.push(system);
        console.log(`✅ 系统 ${systemKey} 处理完成:`, system);
      });

      // 按系统ID排序
      systems.value = systemsList.sort((a, b) => a.id.localeCompare(b.id));
      console.log('✅ 所有系统加载完成:', systems.value);

      ElMessage.success(`系统列表加载成功 (${systems.value.length} 个系统)`);
    } else {
      console.error('❌ 系统数据格式错误:', systemsData);
      systems.value = [];
      ElMessage.error('系统数据格式错误');
    }
  } catch (error: any) {
    console.error('❌ 加载系统列表失败:', error);
    ElMessage.error('加载系统列表失败：' + (error.message || '未知错误'));
    systems.value = [];
  }
};

// 系统名称显示函数（直接返回原始名称）
const getSystemDisplayName = (systemId: string): string => {
  // 直接返回原始系统ID作为显示名称
  return systemId;
};

const handleCreateSystem = async () => {
  if (!createSystemFormRef.value) return

  try {
    await createSystemFormRef.value.validate()
    creatingSystem.value = true

    // 使用用例管理API创建系统
    const systemData = {
      systemName: createSystemForm.systemName,
      subsystem: createSystemForm.subsystem,
      softwareName: createSystemForm.softwareName,
      softwareType: createSystemForm.softwareType,
      securityLevel: createSystemForm.securityLevel,
      runtimeEnv: createSystemForm.runtimeEnv,
      developEnv: createSystemForm.developEnv,
      language: createSystemForm.language,
      version: createSystemForm.version,
      codeTemplate: createSystemForm.codeTemplate,
      developer: createSystemForm.developer,
    }

    console.log('创建系统请求数据:', systemData)

    await apiService.caseManagement.createSystem(systemData)
    ElMessage.success('系统创建成功')

    showCreateSystemDialog.value = false
    resetCreateSystemForm()
    await loadSystems()
  } catch (error: any) {
    console.error('创建系统失败:', error)
    ElMessage.error('创建系统失败：' + (error.message || '未知错误'))
  } finally {
    creatingSystem.value = false
  }
}

const resetCreateSystemForm = () => {
  createSystemForm.systemName = ''
  createSystemForm.subsystem = ''
  createSystemForm.softwareName = ''
  createSystemForm.softwareType = ''
  createSystemForm.securityLevel = ''
  createSystemForm.runtimeEnv = ''
  createSystemForm.developEnv = ''
  createSystemForm.language = ''
  createSystemForm.version = ''
  createSystemForm.codeTemplate = ''
  createSystemForm.developer = ''
  createSystemFormRef.value?.clearValidate()
}

const resetEditSystemForm = () => {
  editSystemForm.systemName = ''
  editSystemForm.subsystem = ''
  editSystemForm.softwareName = ''
  editSystemForm.softwareType = ''
  editSystemForm.securityLevel = ''
  editSystemForm.runtimeEnv = ''
  editSystemForm.developEnv = ''
  editSystemForm.language = ''
  editSystemForm.version = ''
  editSystemForm.codeTemplate = ''
  editSystemForm.developer = ''
  editSystemFormRef.value?.clearValidate()
}

const showEditSystemDialogHandler = (system: any) => {
  // 填充编辑表单数据
  editSystemForm.systemName = system.name
  editSystemForm.subsystem = system.subsystem || ''
  editSystemForm.softwareName = system.softwareName || ''
  editSystemForm.softwareType = system.softwareType || ''
  editSystemForm.securityLevel = system.securityLevel || ''
  editSystemForm.runtimeEnv = system.runtimeEnv || ''
  editSystemForm.developEnv = system.developEnv || ''
  editSystemForm.language = system.language || ''
  editSystemForm.version = system.version || ''
  editSystemForm.codeTemplate = system.codeTemplate || ''
  editSystemForm.developer = system.developer || ''

  showEditSystemDialog.value = true
}

const handleUpdateSystem = async () => {
  if (!editSystemFormRef.value) return

  try {
    await editSystemFormRef.value.validate()
    updatingSystem.value = true

    const updateData = {
      subsystem: editSystemForm.subsystem,
      softwareName: editSystemForm.softwareName,
      softwareType: editSystemForm.softwareType,
      securityLevel: editSystemForm.securityLevel,
      runtimeEnv: editSystemForm.runtimeEnv,
      developEnv: editSystemForm.developEnv,
      language: editSystemForm.language,
      version: editSystemForm.version,
      codeTemplate: editSystemForm.codeTemplate,
      developer: editSystemForm.developer,
    }



    // 使用用例管理API更新系统
    await apiService.caseManagement.updateSystem(editSystemForm.systemName, updateData)
    ElMessage.success('系统更新成功')

    showEditSystemDialog.value = false
    resetEditSystemForm()
    await loadSystems()
  } catch (error: any) {
    console.error('更新系统失败:', error)
    ElMessage.error('更新系统失败：' + (error.message || '未知错误'))
  } finally {
    updatingSystem.value = false
  }
}

const showAddConfigDialogHandler = (system: any) => {
  currentSystem.value = system
  showAddConfigDialog.value = true
}

const handleAddConfig = async () => {
  if (!addConfigFormRef.value || !currentSystem.value) return

  try {
    await addConfigFormRef.value.validate()
    addingConfig.value = true

    const configData = {
      systemName: currentSystem.value.name,
      configItemName: addConfigForm.configItemName,
      subsystem: addConfigForm.subsystem,
      softwareName: addConfigForm.softwareName,
      softwareType: addConfigForm.softwareType,
      securityLevel: addConfigForm.securityLevel,
      runtimeEnv: addConfigForm.runtimeEnv,
      developEnv: addConfigForm.developEnv,
      language: addConfigForm.language,
      version: addConfigForm.version,
      codeTemplate: addConfigForm.codeTemplate,
      developer: addConfigForm.developer,
    }

    // 使用用例管理API创建配置项
    await apiService.caseManagement.createConfigItem(configData)
    ElMessage.success('配置项添加成功')

    showAddConfigDialog.value = false
    resetAddConfigForm()
    await loadSystems()
  } catch (error: any) {
    ElMessage.error('添加配置项失败：' + (error.message || '未知错误'))
  } finally {
    addingConfig.value = false
  }
}

const handleEditConfig = (system: any, config: any) => {
  // 填充编辑表单数据
  editConfigForm.systemName = system.name
  editConfigForm.configItemName = config.name
  editConfigForm.newConfigItemName = config.name
  editConfigForm.subsystem = config.subsystem || ''
  editConfigForm.softwareName = config.softwareName || ''
  editConfigForm.softwareType = config.softwareType || ''
  editConfigForm.securityLevel = config.securityLevel || ''
  editConfigForm.runtimeEnv = config.runtimeEnv || ''
  editConfigForm.developEnv = config.developEnv || ''
  editConfigForm.language = config.language || ''
  editConfigForm.version = config.version || ''
  editConfigForm.codeTemplate = config.codeTemplate || ''
  editConfigForm.developer = config.developer || ''

  showEditConfigDialog.value = true
}

const handleUpdateConfig = async () => {
  if (!editConfigFormRef.value) return

  try {
    await editConfigFormRef.value.validate()
    updatingConfig.value = true

    const updateData = {
      systemName: editConfigForm.systemName,
      configItemName: editConfigForm.configItemName,
      newConfigItemName: editConfigForm.newConfigItemName,
      subsystem: editConfigForm.subsystem,
      softwareName: editConfigForm.softwareName,
      softwareType: editConfigForm.softwareType,
      securityLevel: editConfigForm.securityLevel,
      runtimeEnv: editConfigForm.runtimeEnv,
      developEnv: editConfigForm.developEnv,
      language: editConfigForm.language,
      version: editConfigForm.version,
      codeTemplate: editConfigForm.codeTemplate,
      developer: editConfigForm.developer,
    }

    console.log('更新配置项请求数据:', updateData)

    // 使用用例管理API更新配置项
    await apiService.caseManagement.updateConfigItem(updateData)
    ElMessage.success('配置项更新成功')

    showEditConfigDialog.value = false
    resetEditConfigForm()
    await loadSystems()
  } catch (error: any) {
    console.error('更新配置项失败:', error)
    ElMessage.error('更新配置项失败：' + (error.message || '未知错误'))
  } finally {
    updatingConfig.value = false
  }
}

const handleDeleteConfig = async (system: any, config: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除配置项 "${config.name}" 吗？`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    const configData = {
      systemName: system.name,
      configItemName: config.name  // 修复：使用 configItemName 而不是 configName
    }

    // 使用用例管理API删除配置项
    await apiService.caseManagement.deleteConfigItem(configData)
    ElMessage.success('配置项删除成功')
    await loadSystems()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('删除配置项失败：' + (error.message || '未知错误'))
    }
  }
}



const resetAddConfigForm = () => {
  addConfigForm.configItemName = ''
  addConfigForm.subsystem = ''
  addConfigForm.softwareName = ''
  addConfigForm.softwareType = ''
  addConfigForm.securityLevel = ''
  addConfigForm.runtimeEnv = ''
  addConfigForm.developEnv = ''
  addConfigForm.language = ''
  addConfigForm.version = ''
  addConfigForm.codeTemplate = ''
  addConfigForm.developer = ''
  addConfigFormRef.value?.clearValidate()
}

const resetEditConfigForm = () => {
  editConfigForm.systemName = ''
  editConfigForm.configItemName = ''
  editConfigForm.newConfigItemName = ''
  editConfigForm.subsystem = ''
  editConfigForm.softwareName = ''
  editConfigForm.softwareType = ''
  editConfigForm.securityLevel = ''
  editConfigForm.runtimeEnv = ''
  editConfigForm.developEnv = ''
  editConfigForm.language = ''
  editConfigForm.version = ''
  editConfigForm.codeTemplate = ''
  editConfigForm.developer = ''
  editConfigFormRef.value?.clearValidate()
}





// 监听选项卡切换，自动刷新数据
watch(activeTab, async (newTab, oldTab) => {
  if (newTab !== oldTab) {
    console.log(`权限管理选项卡切换: ${oldTab} -> ${newTab}`)

    switch (newTab) {
      case 'user-permissions':
        // 切换到用户权限分配，刷新用户和系统数据
        await Promise.all([loadUsers(), loadSystems()])
        ElMessage.success('用户权限分配数据已刷新')
        break
      case 'users':
        // 切换到用户管理，刷新用户列表
        await loadUsers()
        ElMessage.success('用户列表数据已刷新')
        break
      case 'systems':
        // 切换到系统配置，刷新系统列表
        await loadSystems()
        ElMessage.success('系统配置数据已刷新')
        break
    }
  }
})

// 页面初始化
onMounted(async () => {
  await Promise.all([loadUsers(), loadSystems()])
})
</script>

<style scoped lang="scss">
@use '@/assets/styles/theme.scss' as theme;

.permission-management {
  padding: 24px;
  background: linear-gradient(
    135deg,
    theme.$color-background 0%,
    theme.$color-background-dark 100%
  );
  min-height: calc(100vh - 60px);

  /* 无权限提示样式 */
  .no-permission-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 80vh;
  }

  .no-permission-content {
    text-align: center;
    padding: 2rem;
    background: theme.$color-container;
    border-radius: theme.$border-radius-large;
    box-shadow: theme.$shadow-strong;
    max-width: 500px;
  }

  .no-permission-icon {
    color: #f56c6c;
    margin-bottom: 1rem;
  }

  .no-permission-title {
    font-size: 2rem;
    color: #f56c6c;
    margin: 1rem 0;
    font-weight: 600;
  }

  .no-permission-message {
    font-size: 1.2rem;
    color: theme.$color-text-primary;
    margin-bottom: 1rem;
  }

  .no-permission-description {
    color: theme.$color-text-secondary;
    margin-bottom: 2rem;
    line-height: 1.6;

    p {
      margin: 0.5rem 0;
    }
  }

  .no-permission-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
  }

  .page-header {
    margin-bottom: 32px;
    text-align: center;

    .page-title {
      font-size: 2rem;
      font-weight: 600;
      color: theme.$color-text-primary;
      margin: 0 0 8px 0;
    }

    .page-subtitle {
      font-size: 1rem;
      color: theme.$color-text-secondary;
      margin: 0;
    }
  }

  .permission-tabs {
    background: theme.$color-container;
    border-radius: theme.$border-radius-large;
    padding: 24px;
    box-shadow: theme.$shadow-soft;

    :deep(.el-tabs__header) {
      margin-bottom: 24px;

      .el-tabs__nav-wrap {
        &::after {
          background-color: theme.$color-border;
        }
      }

      .el-tabs__item {
        color: theme.$color-text-secondary;
        font-weight: 500;

        &.is-active {
          color: theme.$color-primary;
        }

        &:hover {
          color: theme.$color-primary-light;
        }
      }

      .el-tabs__active-bar {
        background-color: theme.$color-primary;
      }
    }
  }

  .module-container {
    .module-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
      padding-bottom: 16px;
      border-bottom: 1px solid theme.$color-border;

      h2 {
        font-size: 1.5rem;
        font-weight: 600;
        color: theme.$color-text-primary;
        margin: 0;
      }

      .module-description {
        color: theme.$color-text-secondary;
        margin: 8px 0 0 0;
        font-size: 0.9rem;
      }
    }
  }

  // 用户管理样式
  .user-table {
    :deep(.el-table) {
      background-color: theme.$color-container;

      .el-table__header {
        background-color: theme.$color-background;

        th {
          background-color: theme.$color-background;
          color: theme.$color-text-primary;
          font-weight: 600;
        }
      }

      .el-table__body {
        tr {
          background-color: theme.$color-container;

          &:hover {
            background-color: theme.$color-background;
          }
        }

        td {
          color: theme.$color-text-primary;
        }
      }
    }
  }

  // 系统配置样式
  .systems-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 24px;

    .system-card {
      :deep(.el-card) {
        background: theme.$color-container;
        border: 1px solid theme.$color-border;
        border-radius: theme.$border-radius-large;
        transition: all theme.$transition-normal;

        &:hover {
          box-shadow: theme.$shadow-medium;
          transform: translateY(-2px);
        }

        .el-card__header {
          background: linear-gradient(
            135deg,
            theme.$color-primary-light 0%,
            theme.$color-primary 100%
          );
          border-bottom: 1px solid theme.$color-border;

          .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .system-name {
              font-weight: 600;
              font-size: 1.1rem;
              color: theme.$color-text-light;
            }

            .header-actions {
              display: flex;
              gap: 8px;
              align-items: center;

              .el-button {
                &.el-button--danger {
                  background-color: #f56c6c;
                  border-color: #f56c6c;
                  color: white;

                  &:hover {
                    background-color: #f78989;
                    border-color: #f78989;
                  }
                }
              }
            }
          }
        }
      }

      .config-tree {
        :deep(.el-tree) {
          background-color: transparent;

          .el-tree-node {
            .el-tree-node__content {
              background-color: transparent;
              color: theme.$color-text-primary;

              &:hover {
                background-color: theme.$color-background;
              }
            }
          }
        }

        .tree-node {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;

          .node-actions {
            opacity: 0;
            transition: opacity theme.$transition-normal;
          }

          &:hover .node-actions {
            opacity: 1;
          }
        }
      }
    }
  }

  // 权限分配样式
  .permission-assignment {
    .assignment-filters {
      margin-bottom: 24px;

      .el-select {
        width: 200px;
      }
    }


  }

  // 对话框样式覆盖
  :deep(.el-dialog) {
    background: theme.$color-container;
    border-radius: theme.$border-radius-large;

    .el-dialog__header {
      background: linear-gradient(135deg, theme.$color-primary-light 0%, theme.$color-primary 100%);
      border-radius: theme.$border-radius-large theme.$border-radius-large 0 0;
      padding: 20px 24px;

      .el-dialog__title {
        color: theme.$color-text-light;
        font-weight: 600;
      }
    }

    .el-dialog__body {
      padding: 24px;
    }

    .el-dialog__footer {
      padding: 16px 24px;
      border-top: 1px solid theme.$color-border;
    }
  }

  // 表单样式覆盖
  :deep(.el-form) {
    .el-form-item__label {
      color: theme.$color-text-primary;
      font-weight: normal;
    }

    .el-input__inner {
      border-color: theme.$color-border;
      background-color: theme.$color-container;
      color: theme.$color-text-primary;

      &:focus {
        border-color: theme.$color-border-focus;
        box-shadow: 0 0 0 2px rgba(theme.$color-primary-accent, 0.2);
      }
    }

    .el-select {
      .el-input__inner {
        cursor: pointer;
      }
    }
  }

  // 按钮样式覆盖
  :deep(.el-button) {
    border-radius: theme.$border-radius-medium;
    font-weight: 500;
    transition: all theme.$transition-normal;

    &.el-button--primary {
      background: linear-gradient(135deg, theme.$color-primary 0%, theme.$color-primary-dark 100%);
      border-color: theme.$color-primary;

      &:hover {
        background: linear-gradient(
          135deg,
          theme.$color-primary-light 0%,
          theme.$color-primary 100%
        );
        border-color: theme.$color-primary-light;
        transform: translateY(-1px);
        box-shadow: theme.$shadow-glow-primary;
      }
    }

    &.el-button--danger {
      &:hover {
        transform: translateY(-1px);
      }
    }
  }

  // 标签样式覆盖
  :deep(.el-tag) {
    border-radius: theme.$border-radius-small;
    font-weight: 500;
  }


}
</style>
