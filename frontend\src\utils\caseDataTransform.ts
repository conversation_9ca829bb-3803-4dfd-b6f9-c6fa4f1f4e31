import type {
  SystemsRawResponse,
  CaseSystemData,
  TestItem,
  CaseData,
  CaseNode,
  TransformedSystemsData,
  CaseLibraryData
} from '@/services/api'

/**
 * 用例数据转换工具类
 * 负责将后端API响应转换为前端可用的树形结构数据
 */
export class CaseDataTransform {

  /**
   * 生成唯一ID
   */
  private static generateId(prefix: string, ...parts: string[]): string {
    return `${prefix}_${parts.join('_')}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 处理用例名称，添加序号后缀
   * @param originalName 原始用例名称
   * @param index 用例索引
   * @returns 格式化后的用例名称
   */
  private static formatCaseName(originalName: string, index: number): string {
    // 如果原始名称已经包含序号后缀（格式：名称-数字），则直接使用
    if (/^.+-\d+$/.test(originalName)) {
      return originalName
    }

    // 否则添加序号后缀
    return `${originalName}-${index + 1}`
  }

  /**
   * 获取用例的显示名称
   * @param originalName 原始用例名称
   * @param index 用例索引
   * @returns 显示名称
   */
  private static getCaseDisplayName(originalName: string, index: number): string {
    return CaseDataTransform.formatCaseName(originalName, index)
  }

  /**
   * 转换用例数据
   * @param caseName 用例名称
   * @param caseData 用例数据
   * @param index 用例索引
   * @param systemKey 系统键
   * @param functionName 功能名称
   * @param testItemName 测试项名称
   * @param tableName 表名称
   * @returns 转换后的用例节点
   */
  private static transformCase(
    caseName: string,
    caseData: CaseData,
    index: number,
    systemKey: string,
    functionName: string,
    testItemName: string,
    tableName?: string
  ): CaseNode {
    const displayName = CaseDataTransform.getCaseDisplayName(caseName, index)

    return {
      id: caseData.id || CaseDataTransform.generateId('case', systemKey, functionName, testItemName, caseName),
      name: displayName, // 使用格式化后的名称
      displayName,
      type: 'testCase', // 更新为 testCase 类型
      selected: caseData.selected,
      deleted: caseData.deleted,
      originalName: caseName,
      systemKey,
      tableName, // 确保用例节点包含tableName字段
      usecaseDetail: (caseData as any).usecaseDetail // 只使用已有的usecaseDetail，不创建默认值
    }
  }

  /**
   * 转换测试项数据
   * @param testItemName 测试项名称
   * @param cases 用例数组
   * @param systemKey 系统键
   * @param functionName 功能名称
   * @param index 测试项索引
   * @param tableName 表名称
   * @returns 转换后的测试项节点
   */
  private static transformTestItem(
    testItemName: string,
    cases: any, // 修改类型，因为实际结构是 {data: {...}, path: Array}
    systemKey: string,
    functionName: string,
    index: number,
    tableName?: string
  ): CaseNode {
    const children: CaseNode[] = []
    let caseIndex = 0

    // 处理用例数据 - 修复API数据结构解析
    console.log(`🔍 处理测试项 ${testItemName} 的用例数据:`, cases)
    console.log(`  - cases类型: ${typeof cases}`)
    console.log(`  - cases是数组: ${Array.isArray(cases)}`)

    // 检查新的数据结构：{data: {...}, path: Array}
    let actualCases: any[] = []

    if (cases && typeof cases === 'object') {
      if (Array.isArray(cases)) {
        // 如果直接是数组，使用原逻辑
        actualCases = cases
        console.log(`  - 直接使用数组，长度: ${cases.length}`)
      } else if (cases.path && Array.isArray(cases.path)) {
        // 如果是 {data: {...}, path: Array} 结构，使用path数组
        actualCases = cases.path
        console.log(`  - 从path属性提取数组，长度: ${cases.path.length}`)
        console.log(`  - path数组内容:`, cases.path)
      } else {
        console.log(`  - 未知的对象结构:`, Object.keys(cases))
      }
    }

    if (actualCases && Array.isArray(actualCases) && actualCases.length > 0) {
      console.log(`  - 开始处理 ${actualCases.length} 个用例数据项`)
      actualCases.forEach((caseData, index) => {
        console.log(`    处理用例数据项 ${index}:`, caseData)
        console.log(`    数据项类型: ${typeof caseData}`)

        // API结构：每个caseData是一个对象，包含用例名称作为键
        if (caseData && typeof caseData === 'object') {
          const entries = Object.entries(caseData)
          console.log(`    对象有 ${entries.length} 个属性:`, entries.map(([key]) => key))

          entries.forEach(([caseName, caseInfo]) => {
            console.log(`      处理用例: ${caseName}`, caseInfo)
            console.log(`      用例信息类型: ${typeof caseInfo}`)
            console.log(`      用例信息有id: ${'id' in (caseInfo as object || {})}`)

            if (typeof caseInfo === 'object' && caseInfo !== null && 'id' in caseInfo) {
              console.log(`      ✅ 创建用例: ${caseName}`)
              children.push(CaseDataTransform.transformCase(
                caseName,
                caseInfo as CaseData,
                caseIndex++,
                systemKey,
                functionName,
                testItemName,
                tableName
              ))
            } else {
              console.log(`      ❌ 跳过用例 ${caseName}: 数据格式不符合要求`)
            }
          })
        } else {
          console.log(`    ❌ 跳过数据项 ${index}: 不是对象类型`)
        }
      })
    } else {
      console.log(`  ❌ 没有有效的用例数据`)
      if (!actualCases) {
        console.log(`    原因: actualCases为null/undefined`)
      } else if (!Array.isArray(actualCases)) {
        console.log(`    原因: actualCases不是数组，类型为 ${typeof actualCases}`)
      } else {
        console.log(`    原因: actualCases数组为空`)
      }
    }

    // 空测试项是正常状态，不需要创建默认节点

    // 生成带序号的测试项名称
    const displayName = `${testItemName}-${index + 1}`

    return {
      id: CaseDataTransform.generateId('testItem', systemKey, functionName, testItemName),
      name: displayName,
      displayName,
      type: 'testItem',
      children,
      systemKey,
      tableName,
      originalName: testItemName, // 保存原始测试项名称
      originalData: (cases as any)?.data || { type: "testMethod" }, // 保存原始data字段
      isExpandable: true,
      isEmpty: children.length === 0,
      canAcceptDrop: true
    }
  }

  /**
   * 转换功能数据
   * @param functionName 功能名称
   * @param functionData 功能数据
   * @param systemKey 系统键
   * @param index 功能索引
   * @returns 转换后的功能节点
   */
  private static transformFunction(
    functionName: string,
    functionData: { tableName: string; path: TestItem[] },
    systemKey: string,
    index: number
  ): CaseNode {
    const children: CaseNode[] = []

    console.log(`🔧 处理功能: ${functionName}`, functionData)

    // 验证功能数据结构
    if (!functionData) {
      console.log(`📁 功能 ${functionName} 数据为空，创建空目录节点`)
      const displayName = `${functionName}-${index + 1}`
      return {
        id: CaseDataTransform.generateId('function', systemKey, functionName),
        name: displayName,
        displayName,
        type: 'function',
        children: [],
        isExpandable: true,
        isEmpty: true
      }
    }

    // 处理测试项路径 - 新的API数据结构
    let testItemIndex = 0
    if (functionData.path && Array.isArray(functionData.path) && functionData.path.length > 0) {
      console.log(`📂 功能 ${functionName} 有 ${functionData.path.length} 个测试项组`)
      functionData.path.forEach((testItemGroup, groupIndex) => {
        console.log(`📁 处理测试项组 ${groupIndex}:`, testItemGroup)
        console.log(`  测试项组类型: ${typeof testItemGroup}`)
        console.log(`  测试项组是对象: ${testItemGroup && typeof testItemGroup === 'object'}`)

        if (testItemGroup && typeof testItemGroup === 'object') {
          const entries = Object.entries(testItemGroup)
          console.log(`  测试项组有 ${entries.length} 个测试项:`, entries.map(([key]) => key))

          entries.forEach(([testItemName, cases]) => {
            console.log(`🧪 处理测试项: ${testItemName}`)
            console.log(`  测试项数据:`, cases)
            console.log(`  测试项数据类型: ${typeof cases}`)
            console.log(`  测试项是数组: ${Array.isArray(cases)}`)
            if (Array.isArray(cases)) {
              console.log(`  测试项数组长度: ${cases.length}`)
            }

            children.push(CaseDataTransform.transformTestItem(
              testItemName,
              cases,
              systemKey,
              functionName,
              testItemIndex++,
              functionData.tableName
            ))
          })
        } else {
          console.log(`  ❌ 跳过测试项组 ${groupIndex}: 不是对象类型`)
        }
      })
    } else {
      console.log(`📁 功能 ${functionName} 没有测试项路径数据`)
      if (!functionData.path) {
        console.log(`  原因: path属性不存在`)
      } else if (!Array.isArray(functionData.path)) {
        console.log(`  原因: path不是数组，类型为 ${typeof functionData.path}`)
      } else {
        console.log(`  原因: path数组为空`)
      }
    }

    // 空白配置项不再自动创建默认测试项
    if (children.length === 0) {
      console.log(`📁 功能 ${functionName} 是空目录，保持为空`)
    }

    // 生成带序号的功能名称
    const displayName = `${functionName}-${index + 1}`

    return {
      id: CaseDataTransform.generateId('function', systemKey, functionName),
      name: displayName,
      displayName,
      type: 'function',
      children,
      tableName: functionData?.tableName,
      originalName: functionName, // 保存原始配置项名称
      originalData: (functionData as any)?.data || { type: "config" }, // 保存原始data字段
      systemKey,
      isExpandable: true,
      isEmpty: children.length === 0
    }
  }

  /**
   * 转换系统数据
   * @param systemKey 系统键
   * @param systemData 系统数据
   * @returns 转换后的系统节点
   */
  private static transformSystem(systemKey: string, systemData: CaseSystemData): CaseNode {
    const children: CaseNode[] = []

    // 处理功能路径 - 新的API数据结构
    let functionIndex = 0
    if (systemData.path && Array.isArray(systemData.path) && systemData.path.length > 0) {
      systemData.path.forEach(functionGroup => {
        if (functionGroup && typeof functionGroup === 'object') {
          Object.entries(functionGroup).forEach(([functionName, functionData]) => {
            children.push(CaseDataTransform.transformFunction(
              functionName,
              functionData,
              systemKey,
              functionIndex++
            ))
          })
        }
      })
    } else {
      // 空目录是正常状态，不是错误
      console.log(`📁 系统 ${systemKey} 是空目录（可添加配置项）`)
    }

    // 生成系统显示名称
    const displayName = CaseDataTransform.getSystemDisplayName(systemKey)

    return {
      id: CaseDataTransform.generateId('system', systemKey),
      name: systemKey,
      displayName,
      type: 'system',
      children,
      systemKey,
      originalData: systemData.data || { type: "system" }, // 保存系统的原始data字段
      isExpandable: true,
      isEmpty: children.length === 0
    }
  }

  /**
   * 获取系统显示名称
   * @param systemKey 系统键
   * @returns 系统显示名称
   */
  private static getSystemDisplayName(systemKey: string): string {
    // 根据系统键生成显示名称
    const systemMap: Record<string, string> = {
      'c1': '系统A',
      'c2': '系统B',
      'c3': '系统C',
      'c4': '系统D',
      'c5': '系统E'
    }

    return systemMap[systemKey] || `系统${systemKey.toUpperCase()}`
  }

  /**
   * 统计用例总数
   * @param systems 系统节点数组
   * @returns 用例总数
   */
  private static countTotalCases(systems: CaseNode[]): number {
    let total = 0

    const countCases = (node: CaseNode): void => {
      if (node.type === 'testCase') { // 更新为 testCase 类型
        total++
      } else if (node.children) {
        node.children.forEach(countCases)
      }
    }

    systems.forEach(countCases)
    return total
  }

  /**
   * 验证和修复API响应数据结构
   * @param rawData 原始API响应数据
   * @returns 修复后的数据
   */
  private static validateAndFixApiResponse(rawData: any): SystemsRawResponse {
    console.log('🔧 验证API响应数据结构...')

    if (!rawData || typeof rawData !== 'object') {
      console.warn('⚠️ API响应数据无效，返回空对象')
      return {}
    }

    // 如果数据被包装在额外的层级中，尝试提取
    let actualData = rawData

    // 检查是否有 data 字段包装
    if (rawData.data && typeof rawData.data === 'object') {
      console.log('📦 检测到数据被包装在 data 字段中')
      actualData = rawData.data
    }

    // 验证数据结构
    const validatedData: SystemsRawResponse = {}

    Object.entries(actualData).forEach(([key, value]) => {
      // 验证系统键的基本有效性（只检查类型和非空）
      if (typeof key !== 'string' || key.trim() === '') {
        console.warn(`⚠️ 跳过无效的系统键: "${key}" (非字符串或空字符串)`, value)
        return
      }

      // 验证系统数据结构
      if (typeof value === 'object' && value !== null) {
        // 检查必需的字段是否存在
        const hasRequiredFields = (value as any).path !== undefined
        const hasSystemMetadata = (value as any).subsystem !== undefined ||
                                 (value as any).softwareName !== undefined ||
                                 (value as any).version !== undefined

        if (hasRequiredFields || hasSystemMetadata) {
          validatedData[key] = value as any
          console.log(`✅ 验证通过的系统: "${key}"`)
        } else {
          console.warn(`⚠️ 跳过数据结构不完整的系统: ${key}`, value)
        }
      } else {
        console.warn(`⚠️ 跳过无效的系统数据: ${key} (非对象类型)`, value)
      }
    })

    console.log('✅ 数据结构验证完成，有效系统数量:', Object.keys(validatedData).length)
    return validatedData
  }

  /**
   * 主转换函数：将后端响应转换为前端树形结构
   * @param rawData 后端原始响应数据
   * @returns 转换后的前端数据结构
   */
  public static transformSystemsData(rawData: SystemsRawResponse): TransformedSystemsData {
    const systems: CaseNode[] = []

    // 验证和修复数据结构
    const validatedData = CaseDataTransform.validateAndFixApiResponse(rawData)

    // 添加调试日志
    console.log('🔍 开始转换系统数据:', validatedData)
    console.log('📊 系统数量:', Object.keys(validatedData).length)

    // 遍历所有系统
    Object.entries(validatedData).forEach(([systemKey, systemData]) => {
      console.log(`🏗️ 处理系统: ${systemKey}`, systemData)

      // 验证系统数据结构
      if (!systemData) {
        console.warn(`⚠️ 系统 ${systemKey} 数据为空`)
        return
      }

      if (!systemData.path || (Array.isArray(systemData.path) && systemData.path.length === 0)) {
        console.log(`📁 系统 ${systemKey} 是空目录（正常状态）`)
      }

      try {
        systems.push(CaseDataTransform.transformSystem(systemKey, systemData))
        console.log(`✅ 系统 ${systemKey} 转换成功`)
      } catch (error) {
        console.error(`❌ 系统 ${systemKey} 转换失败:`, error)
        console.log('📋 失败的系统数据:', systemData)
      }
    })

    // 统计数据
    const totalCases = CaseDataTransform.countTotalCases(systems)
    const totalSystems = systems.length
    const { productLibraryCases, candidateLibraryCases } = CaseDataTransform.countLibraryCases(systems)

    return {
      systems,
      totalCases,
      totalSystems,
      productLibraryCases,
      candidateLibraryCases
    }
  }

  /**
   * 分离产品库和备选库数据
   * @param rawData 后端原始响应数据
   * @returns 分离后的库数据
   */
  public static separateLibraryData(rawData: SystemsRawResponse): CaseLibraryData {
    console.log('📚 开始分离产品库和备选库数据...')

    const allSystems = CaseDataTransform.transformSystemsData(rawData)
    console.log('🔄 转换后的系统数据:', allSystems)

    const productLibrary: CaseNode[] = []
    const candidateLibrary: CaseNode[] = []

    // 为每个系统创建产品库和备选库版本
    allSystems.systems.forEach((system, index) => {
      console.log(`📂 处理系统 ${index + 1}/${allSystems.systems.length}: ${system.displayName}`)

      // 产品库：始终显示前三级结构，包含选中的用例
      const productSystem = CaseDataTransform.createProductLibrarySystem(system)

      // 备选库：显示完整四级结构，包含未选中的用例
      const candidateSystem = CaseDataTransform.createCandidateLibrarySystem(system)

      console.log(`  - 产品库系统:`, productSystem)
      console.log(`  - 备选库系统:`, candidateSystem)

      const hasProductCases = CaseDataTransform.hasAnyCases(productSystem)
      const hasCandidateCases = CaseDataTransform.hasAnyCases(candidateSystem)

      console.log(`  - 产品库有用例: ${hasProductCases}`)
      console.log(`  - 备选库有用例: ${hasCandidateCases}`)

      // 产品库：始终添加系统（显示前三级结构）
      productLibrary.push(productSystem)
      console.log(`  ✅ 添加到产品库（显示前三级结构）`)

      // 备选库：始终添加所有系统（显示完整三级结构）
      candidateLibrary.push(candidateSystem)
      console.log(`  ✅ 添加到备选库（${hasCandidateCases ? '有' : '没有'}未选中用例）`)
    })

    const result = {
      productLibrary,
      candidateLibrary,
      totalCases: allSystems.totalCases,
      productLibraryCases: allSystems.productLibraryCases,
      candidateLibraryCases: allSystems.candidateLibraryCases
    }

    console.log('📊 库分离结果:')
    console.log(`  - 产品库系统数: ${result.productLibrary.length}`)
    console.log(`  - 备选库系统数: ${result.candidateLibrary.length}`)
    console.log(`  - 产品库用例数: ${result.productLibraryCases}`)
    console.log(`  - 备选库用例数: ${result.candidateLibraryCases}`)
    console.log('📚 库分离完成:', result)

    return result
  }

  /**
   * 反向转换：将前端数据转换为后端格式（用于保存）
   * @param caseNode 用例节点
   * @returns 后端格式的用例数据
   */
  public static transformCaseToBackend(caseNode: CaseNode): any {
    if (caseNode.type !== 'testCase') { // 更新为 testCase 类型
      throw new Error('只能转换用例类型的节点')
    }

    return {
      [caseNode.originalName || caseNode.name]: {
        id: caseNode.id,
        selected: caseNode.selected || false,
        deleted: caseNode.deleted || false
      }
    }
  }

  /**
   * 查找指定ID的节点
   * @param systems 系统数组
   * @param targetId 目标ID
   * @returns 找到的节点或null
   */
  public static findNodeById(systems: CaseNode[], targetId: string): CaseNode | null {
    const findNode = (nodes: CaseNode[]): CaseNode | null => {
      for (const node of nodes) {
        if (node.id === targetId) {
          return node
        }
        if (node.children) {
          const found = findNode(node.children)
          if (found) return found
        }
      }
      return null
    }

    return findNode(systems)
  }

  /**
   * 获取节点的完整路径
   * @param systems 系统数组
   * @param targetId 目标ID
   * @returns 节点路径数组
   */
  public static getNodePath(systems: CaseNode[], targetId: string): CaseNode[] {
    const findPath = (nodes: CaseNode[], path: CaseNode[] = []): CaseNode[] | null => {
      for (const node of nodes) {
        const currentPath = [...path, node]

        if (node.id === targetId) {
          return currentPath
        }

        if (node.children) {
          const found = findPath(node.children, currentPath)
          if (found) return found
        }
      }
      return null
    }

    return findPath(systems) || []
  }

  /**
   * 统计产品库和备选库用例数量
   * @param systems 系统数组
   * @returns 库分类统计
   */
  private static countLibraryCases(systems: CaseNode[]): { productLibraryCases: number; candidateLibraryCases: number } {
    let productLibraryCases = 0
    let candidateLibraryCases = 0

    console.log('🔢 开始统计库用例数量，系统数:', systems.length)

    const countInNode = (node: CaseNode, depth: number = 0) => {
      const indent = '  '.repeat(depth)
      console.log(`${indent}检查节点: ${node.displayName}, type: ${node.type}, selected: ${node.selected}`)

      if (node.type === 'testCase') { // 更新为 testCase 类型
        if (node.selected) {
          productLibraryCases++
          console.log(`${indent}  ✅ 产品库用例 +1 (总计: ${productLibraryCases})`)
        } else {
          candidateLibraryCases++
          console.log(`${indent}  📋 备选库用例 +1 (总计: ${candidateLibraryCases})`)
        }
      }

      if (node.children && node.children.length > 0) {
        console.log(`${indent}  有 ${node.children.length} 个子节点`)
        node.children.forEach(child => countInNode(child, depth + 1))
      }
    }

    systems.forEach((system, index) => {
      console.log(`🏢 统计系统 ${index + 1}: ${system.displayName}`)
      countInNode(system)
    })

    console.log(`📊 统计结果: 产品库 ${productLibraryCases} 个, 备选库 ${candidateLibraryCases} 个`)
    return { productLibraryCases, candidateLibraryCases }
  }

  /**
   * 为产品库创建系统节点（显示前三级结构，包含选中的用例）
   * @param system 系统节点
   * @returns 产品库系统节点
   */
  private static createProductLibrarySystem(system: CaseNode): CaseNode {
    const productSystem: CaseNode = {
      ...system,
      children: []
    }

    if (system.children) {
      system.children.forEach(functionNode => {
        const productFunction: CaseNode = {
          ...functionNode,
          children: []
        }

        if (functionNode.children) {
          functionNode.children.forEach(testItemNode => {
            const productTestItem: CaseNode = {
              ...testItemNode,
              children: []
            }

            // 添加选中的用例
            if (testItemNode.children) {
              const selectedCases = testItemNode.children.filter(caseNode =>
                caseNode.type === 'testCase' && caseNode.selected === true
              )
              productTestItem.children = selectedCases
            }

            // 始终添加测试项到产品库（即使没有选中的用例）
            productFunction.children!.push(productTestItem)
          })
        }

        // 始终添加功能到产品库
        productSystem.children!.push(productFunction)
      })
    }

    return productSystem
  }

  /**
   * 为备选库创建系统节点（显示完整三级结构，第四级只显示未选中的用例）
   * @param system 系统节点
   * @returns 备选库系统节点
   */
  private static createCandidateLibrarySystem(system: CaseNode): CaseNode {
    console.log(`🔍 创建备选库系统: ${system.displayName}`)

    const candidateSystem: CaseNode = {
      ...system,
      children: []
    }

    let totalUnselectedCases = 0

    if (system.children) {
      console.log(`  - 系统有 ${system.children.length} 个功能`)

      system.children.forEach((functionNode, funcIndex) => {
        console.log(`    处理功能 ${funcIndex + 1}: ${functionNode.displayName}`)

        const candidateFunction: CaseNode = {
          ...functionNode,
          children: []
        }

        if (functionNode.children) {
          console.log(`      功能有 ${functionNode.children.length} 个测试项`)

          functionNode.children.forEach((testItemNode, testIndex) => {
            console.log(`        处理测试项 ${testIndex + 1}: ${testItemNode.displayName}`)

            const candidateTestItem: CaseNode = {
              ...testItemNode,
              children: []
            }

            // 添加未选中的用例
            if (testItemNode.children) {
              console.log(`          测试项有 ${testItemNode.children.length} 个用例`)
              console.log(`          测试项子节点详情:`, testItemNode.children.map(child => ({
                name: child.displayName,
                type: child.type,
                selected: child.selected,
                id: child.id
              })))

              const unselectedCases = testItemNode.children.filter(caseNode => {
                const isTestCase = caseNode.type === 'testCase'
                const isUnselected = caseNode.selected !== true
                console.log(`            用例 ${caseNode.displayName}: type=${caseNode.type}, selected=${caseNode.selected}, 符合条件=${isTestCase && isUnselected}`)
                console.log(`            用例完整数据:`, caseNode)
                return isTestCase && isUnselected
              })

              candidateTestItem.children = unselectedCases
              totalUnselectedCases += unselectedCases.length
              console.log(`          找到 ${unselectedCases.length} 个未选中用例`)
            }

            // 始终添加测试项到备选库（即使没有未选中的用例）
            candidateFunction.children!.push(candidateTestItem)
            console.log(`          ✅ 添加测试项到功能（${candidateTestItem.children?.length || 0} 个用例）`)
          })
        }

        // 始终添加功能到备选库
        candidateSystem.children!.push(candidateFunction)
        console.log(`      ✅ 添加功能到系统`)
      })
    }

    console.log(`  - 系统总计未选中用例: ${totalUnselectedCases}`)
    console.log(`  - 最终候选库系统结构:`, candidateSystem)

    return candidateSystem
  }

  /**
   * 根据用例状态过滤系统节点
   * @param system 系统节点
   * @param selected 是否选中（true=产品库，false=备选库）
   * @returns 过滤后的系统节点
   */
  private static filterSystemByCaseStatus(system: CaseNode, selected: boolean): CaseNode {
    const filteredSystem: CaseNode = {
      ...system,
      children: []
    }

    if (system.children) {
      system.children.forEach(functionNode => {
        const filteredFunction: CaseNode = {
          ...functionNode,
          children: []
        }

        if (functionNode.children) {
          functionNode.children.forEach(testItemNode => {
            const filteredTestItem: CaseNode = {
              ...testItemNode,
              children: []
            }

            if (testItemNode.children) {
              const filteredCases = testItemNode.children.filter(caseNode =>
                caseNode.type === 'testCase' && caseNode.selected === selected
              )

              if (filteredCases.length > 0) {
                filteredTestItem.children = filteredCases
                filteredFunction.children!.push(filteredTestItem)
              }
            }
          })

          if (filteredFunction.children!.length > 0) {
            filteredSystem.children!.push(filteredFunction)
          }
        }
      })
    }

    return filteredSystem
  }

  /**
   * 检查系统节点是否包含任何用例
   * @param system 系统节点
   * @returns 是否包含用例
   */
  private static hasAnyCases(system: CaseNode): boolean {
    if (system.type === 'testCase') { // 更新为 testCase 类型
      return true
    }

    if (system.children) {
      return system.children.some(child => CaseDataTransform.hasAnyCases(child))
    }

    return false
  }
}

// 导出便捷函数
export const transformSystemsData = CaseDataTransform.transformSystemsData
export const separateLibraryData = CaseDataTransform.separateLibraryData
export const findNodeById = CaseDataTransform.findNodeById
export const getNodePath = CaseDataTransform.getNodePath
