import axios, { type AxiosInstance, type AxiosResponse, type AxiosError } from 'axios'
import type { LoginForm, UserProfile } from '@/types/api/user'
import { tokenManager } from '@/utils/tokenManager'

// 登录响应接口
interface LoginResponse {
  user_info: UserProfile
  auth_token: string
  expires_in?: number
}

// API响应接口
interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
}

// 代理通道管理器
class ProxyChannelManager {
  private static channels = new Map<string, AxiosInstance>()
  private static baseURL = '/api'

  /**
   * 清理请求头值，确保只包含ISO-8859-1字符
   */
  public static sanitizeHeaderValue(value: string): string {
    try {
      // 检查是否包含非ASCII字符
      if (/[^\x20-\x7E]/.test(value)) {
        // 如果包含非ASCII字符，进行Base64编码
        return btoa(encodeURIComponent(value))
      }
      return value
    } catch (error) {
      // 如果编码失败，返回一个安全的默认值
      console.warn('请求头值清理失败:', error)
      return `encoded_${Date.now()}`
    }
  }

  static createChannel(channelId: string, userId: string): AxiosInstance {
    // 清理请求头值，确保不包含非ASCII字符
    const safeUserId = this.sanitizeHeaderValue(userId)
    const safeChannelId = this.sanitizeHeaderValue(channelId)

    const instance = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'X-User-ID': safeUserId,
        'X-Channel-ID': safeChannelId,
        'X-Isolated-Context': 'true',
      },
      withCredentials: false, // 禁用cookies，避免冲突
    })

    // 请求拦截器 - 自动注入认证token
    instance.interceptors.request.use(
      async (config) => {
        // 智能获取token（如果需要会自动刷新）
        const tokenResult = await tokenManager.smartRefreshToken()

        if (tokenResult.success && tokenResult.accessToken) {
          config.headers['Authorization'] = `Bearer ${tokenResult.accessToken}`
        } else if (tokenResult.message?.includes('Token仍然有效')) {
          // Token仍然有效，直接使用
          const token = tokenManager.getAccessToken()
          if (token) {
            config.headers['Authorization'] = `Bearer ${token}`
          }
        }

        // 为每个请求添加时间戳，避免缓存
        config.headers['X-Request-Time'] = Date.now().toString()

        // 确保channelId只包含ASCII字符，避免HTTP请求头编码问题
        const safeChannelId = ProxyChannelManager.sanitizeHeaderValue(channelId)
        config.headers['X-Request-ID'] =
          `${safeChannelId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

        return config
      },
      (error) => {
        console.error(`[${channelId}] 请求拦截器错误:`, error)
        return Promise.reject(error)
      },
    )

    // 响应拦截器 - 处理认证错误和自动重试
    instance.interceptors.response.use(
      (response: AxiosResponse) => {
        return response.data
      },
      async (error: AxiosError) => {
        const originalRequest = error.config

        // 处理401认证错误
        if (error.response?.status === 401 && originalRequest && !(originalRequest as any)._retry) {
          (originalRequest as any)._retry = true

          try {
            // 使用统一token管理器刷新token
            const tokenResult = await tokenManager.smartRefreshToken()

            if (tokenResult.success && tokenResult.accessToken) {
              // 更新请求头并重试
              if (originalRequest.headers) {
                originalRequest.headers['Authorization'] = `Bearer ${tokenResult.accessToken}`
              }
              return instance(originalRequest)
            } else {
              console.error(`[${channelId}] Token刷新失败:`, tokenResult.message)
              return Promise.reject(new Error(tokenResult.message || 'Token刷新失败'))
            }
          } catch (refreshError) {
            console.error(`[${channelId}] Token刷新异常:`, refreshError)
            return Promise.reject(refreshError)
          }
        }

        console.error(`[${channelId}] API请求失败:`, error)
        return Promise.reject(error)
      },
    )

    this.channels.set(channelId, instance)
    return instance
  }

  static getChannel(channelId: string): AxiosInstance | null {
    return this.channels.get(channelId) || null
  }

  static removeChannel(channelId: string): void {
    this.channels.delete(channelId)
  }

  static getChannelCount(): number {
    return this.channels.size
  }

  static getAllChannels(): string[] {
    return Array.from(this.channels.keys())
  }
}

// 独立API服务类
export class IsolatedApiService {
  private userId: string
  private channelId: string
  private instance: AxiosInstance
  private token: string | null = null

  constructor(userId: string, channelId: string) {
    this.userId = userId
    this.channelId = channelId

    // 获取或创建代理通道
    this.instance =
      ProxyChannelManager.getChannel(channelId) ||
      ProxyChannelManager.createChannel(channelId, userId)
  }

  /**
   * 清理请求头值，确保只包含ISO-8859-1字符
   */
  private sanitizeHeaderValue(value: string): string {
    try {
      // 检查是否包含非ASCII字符
      if (/[^\x20-\x7E]/.test(value)) {
        // 如果包含非ASCII字符，进行Base64编码
        return btoa(encodeURIComponent(value))
      }
      return value
    } catch (error) {
      // 如果编码失败，返回一个安全的默认值
      console.warn('请求头值清理失败:', error)
      return `encoded_${Date.now()}`
    }
  }

  /**
   * 设置认证Token（同时更新统一token管理器）
   */
  setToken(token: string, refreshToken?: string, rememberMe: boolean = false): void {
    this.token = token
    this.instance.defaults.headers.common['Authorization'] = `Bearer ${token}`

    // 同步到统一token管理器
    tokenManager.setTokens(token, refreshToken, rememberMe)
  }

  /**
   * 清除认证Token
   */
  clearToken(): void {
    this.token = null
    delete this.instance.defaults.headers.common['Authorization']

    // 同步清除统一token管理器
    tokenManager.clearTokens()
  }

  /**
   * 执行GET请求
   */
  async get<T = any>(url: string, params?: any): Promise<T> {
    try {
      const response = await this.instance.get(url, { params })
      return this.handleResponse(response)
    } catch (error) {
      throw this.handleError(error as AxiosError)
    }
  }

  /**
   * 执行POST请求
   */
  async post<T = any>(url: string, data?: any): Promise<T> {
    try {
      const response = await this.instance.post(url, data)
      return this.handleResponse(response)
    } catch (error) {
      throw this.handleError(error as AxiosError)
    }
  }

  /**
   * 执行PUT请求
   */
  async put<T = any>(url: string, data?: any): Promise<T> {
    try {
      const response = await this.instance.put(url, data)
      return this.handleResponse(response)
    } catch (error) {
      throw this.handleError(error as AxiosError)
    }
  }

  /**
   * 执行DELETE请求
   */
  async delete<T = any>(url: string, params?: any): Promise<T> {
    try {
      const response = await this.instance.delete(url, { params })
      return this.handleResponse(response)
    } catch (error) {
      throw this.handleError(error as AxiosError)
    }
  }

  /**
   * 用户登录
   */
  async login(loginForm: LoginForm): Promise<ApiResponse<LoginResponse>> {
    try {
      const response = await this.post<LoginResponse>('/v1/multi-user/auth/login', {
        username: loginForm.username,
        password: loginForm.password,
      })

      if (response && response.auth_token) {
        // 设置token（支持双token机制）
        this.setToken(
          response.auth_token,
          (response as any).refresh_token, // 如果后端返回refresh token
          true // 默认记住登录状态
        )

        return {
          success: true,
          data: response,
        }
      }

      return {
        success: false,
        message: '登录响应格式无效',
      }
    } catch (error: any) {
      return {
        success: false,
        message: error.message || '登录失败',
      }
    }
  }

  /**
   * 用户登出
   */
  async logout(): Promise<ApiResponse<void>> {
    try {
      await this.post('/v1/multi-user/auth/logout')
      this.clearToken()

      return { success: true }
    } catch (error: any) {
      console.error(`[${this.channelId}] 登出失败:`, error)

      // 即使登出失败，也清除本地Token
      this.clearToken()

      return {
        success: false,
        message: error.message || '登出失败',
      }
    }
  }

  /**
   * 获取用户资料
   */
  async getProfile(): Promise<ApiResponse<UserProfile>> {
    try {
      const response = await this.get<UserProfile>('/v1/multi-user/auth/profile')

      return {
        success: true,
        data: response,
      }
    } catch (error: any) {
      console.error(`[${this.channelId}] 获取用户资料失败:`, error)
      return {
        success: false,
        message: error.message || '获取用户资料失败',
      }
    }
  }

  /**
   * 刷新Token
   */
  async refreshToken(): Promise<ApiResponse<{ auth_token: string; refresh_token?: string }>> {
    try {
      // 使用当前的refresh token进行刷新
      const refreshToken = tokenManager.getRefreshToken()
      if (!refreshToken) {
        return {
          success: false,
          message: '没有可用的刷新令牌',
        }
      }

      const response = await this.post<{ auth_token: string; refresh_token?: string }>(
        '/v1/multi-user/auth/refresh',
        { refresh_token: refreshToken }
      )

      if (response && response.auth_token) {
        // 更新token（保持记住登录状态）
        const rememberMe = !!localStorage.getItem('access_token')
        this.setToken(response.auth_token, (response as any).refresh_token, rememberMe)

        return {
          success: true,
          data: response,
          auth_token: response.auth_token, // 使用正确的属性名
        } as any
      }

      return {
        success: false,
        message: 'Token刷新响应格式无效',
      }
    } catch (error: any) {
      console.error(`[${this.channelId}] Token刷新失败:`, error)
      return {
        success: false,
        message: error.message || 'Token刷新失败',
      }
    }
  }

  /**
   * 验证Token有效性
   */
  async validateToken(): Promise<boolean> {
    try {
      await this.get('/v1/multi-user/auth/validate')

      return true
    } catch (error: any) {
      console.error(`[${this.channelId}] Token验证失败:`, error)
      return false
    }
  }

  /**
   * 处理响应
   */
  private handleResponse<T>(response: AxiosResponse): T {
    // 处理后端的标准响应格式
    if (response.data && typeof response.data === 'object') {
      if (response.data.data !== undefined) {
        return response.data.data
      }
      if (response.data.status === 'success' && response.data.data !== undefined) {
        return response.data.data
      }
    }

    return response.data
  }

  /**
   * 处理错误
   */
  private handleError(error: AxiosError): Error {
    let message = '请求失败'

    if (error.response) {
      const { status, data } = error.response

      if (data && typeof data === 'object') {
        if ((data as any).message) {
          message = (data as any).message
        } else if ((data as any).error) {
          message = (data as any).error
        }
      }

      message = `${message} (状态码: ${status})`
    } else if (error.request) {
      message = '网络连接失败'
    } else {
      message = error.message || '未知错误'
    }

    const customError = new Error(message)
    customError.name = 'ApiError'
    return customError
  }

  /**
   * 获取通道信息
   */
  getChannelInfo() {
    return {
      userId: this.userId,
      channelId: this.channelId,
      hasToken: !!this.token,
      channelCount: ProxyChannelManager.getChannelCount(),
    }
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    this.clearToken()
    ProxyChannelManager.removeChannel(this.channelId)
  }
}

// 导出代理通道管理器（用于调试）
export { ProxyChannelManager }
