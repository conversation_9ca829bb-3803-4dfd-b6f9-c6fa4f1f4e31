/**
 * 悬浮窗管理器
 * 负责创建和管理录制控制悬浮窗
 */

interface FloatingWindowOptions {
  title?: string
  width?: number
  height?: number
  x?: number
  y?: number
}

interface EventHandler {
  (data?: any): void
}

class FloatingWindowManager {
  private floatingWindow: Window | null = null
  private eventHandlers: Map<string, EventHandler[]> = new Map()

  /**
   * 打开悬浮窗
   */
  async openFloatingWindow(options: FloatingWindowOptions = {}): Promise<boolean> {
    try {
      // 如果已经有悬浮窗，先关闭
      if (this.floatingWindow && !this.floatingWindow.closed) {
        this.floatingWindow.close()
      }

      const {
        width = 400,
        height = 80,
        x = Math.max(
          50,
          Math.min(window.screen.availWidth - 400 - 50, window.screen.availWidth - 450),
        ),
        y = Math.max(50, Math.min(window.screen.availHeight - 80 - 50, 100)),
        title = '录制控制台',
      } = options

      // 创建悬浮窗
      const features = [
        `width=${width}`,
        `height=${height}`,
        `left=${x}`,
        `top=${y}`,
        'resizable=yes',
        'scrollbars=no',
        'toolbar=no',
        'menubar=no',
        'location=no',
        'status=no',
      ].join(',')

      this.floatingWindow = window.open('', '_blank', features)

      if (!this.floatingWindow) {
        return false
      }

      // 设置悬浮窗内容
      this.floatingWindow.document.write(this.createFloatingWindowHtml(title))
      this.floatingWindow.document.close()

      // 设置窗口标题
      this.floatingWindow.document.title = title

      // 初始化悬浮窗功能
      this.initializeFloatingWindow()

      return true
    } catch (_error: any) {
      return false
    }
  }

  /**
   * 关闭悬浮窗
   */
  closeFloatingWindow(): void {
    if (this.floatingWindow && !this.floatingWindow.closed) {
      this.floatingWindow.close()
      this.floatingWindow = null
    }
  }

  /**
   * 聚焦悬浮窗
   */
  focusFloatingWindow(): void {
    if (this.floatingWindow && !this.floatingWindow.closed) {
      this.floatingWindow.focus()
    }
  }

  /**
   * 添加事件监听器
   */
  on(event: string, handler: EventHandler): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, [])
    }
    this.eventHandlers.get(event)!.push(handler)
  }

  /**
   * 移除事件监听器
   */
  off(event: string, handler?: EventHandler): void {
    if (!this.eventHandlers.has(event)) return

    if (handler) {
      const handlers = this.eventHandlers.get(event)!
      const index = handlers.indexOf(handler)
      if (index > -1) {
        handlers.splice(index, 1)
      }
    } else {
      this.eventHandlers.set(event, [])
    }
  }

  /**
   * 触发事件
   */
  private emit(event: string, data?: any): void {
    const handlers = this.eventHandlers.get(event) || []
    handlers.forEach((handler) => {
      try {
        handler(data)
      } catch (_error: any) {
        // 事件处理器执行失败处理
      }
    })
  }

  /**
   * 创建悬浮窗HTML内容
   */
  private createFloatingWindowHtml(title: string): string {
    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${title}</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    html, body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      background: #ffffff;
      width: 400px;
      height: 80px;
      overflow: hidden;
      user-select: none;
    }

    .container {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      padding: 8px 12px;
      gap: 8px;
      background: #fff;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
    }

    .input-container {
      flex: 1;
      min-width: 120px;
    }

    .input-container input {
      width: 100%;
      padding: 6px 10px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      font-size: 13px;
      outline: none;
      height: 32px;
    }

    .input-container input:focus {
      border-color: #409eff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }

    .btn {
      width: 32px;
      height: 32px;
      border: none;
      border-radius: 50%;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s;
      flex-shrink: 0;
    }

    .btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .btn:hover:not(:disabled) {
      transform: scale(1.05);
    }

    .btn-start {
      background: #67C23A;
    }

    .btn-start:hover:not(:disabled) {
      background: #529B2E;
    }

    .btn-pause {
      background: #E6A23C;
      border-radius: 8px;
    }

    .btn-pause:hover:not(:disabled) {
      background: #CF9236;
    }

    .btn-resume {
      background: #67C23A;
      border-radius: 8px;
    }

    .btn-resume:hover:not(:disabled) {
      background: #529B2E;
    }

    .btn-stop {
      background: #909399;
      border-radius: 8px;
    }

    .btn-stop:hover:not(:disabled) {
      background: #73767A;
    }

    .btn-screenshot {
      background: #409EFF;
      border-radius: 8px;
    }

    .btn-screenshot:hover:not(:disabled) {
      background: #337ECC;
    }

    .btn svg {
      width: 14px;
      height: 14px;
      fill: white;
    }

    .hidden {
      display: none !important;
    }

    /* 自定义提示组件样式 */
    .custom-toast {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 10000;
      max-width: 280px;
      width: 90%;
    }

    .toast-content {
      background: rgba(0, 0, 0, 0.9);
      border-radius: 8px;
      padding: 12px;
      display: flex;
      align-items: center;
      gap: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .toast-icon {
      font-size: 16px;
      flex-shrink: 0;
    }

    .toast-message {
      flex: 1;
      font-size: 12px;
      line-height: 1.4;
      color: white;
      word-break: break-word;
    }

    .toast-close {
      background: none;
      border: none;
      color: rgba(255, 255, 255, 0.7);
      font-size: 16px;
      cursor: pointer;
      padding: 0;
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      flex-shrink: 0;
    }

    .toast-close:hover {
      background: rgba(255, 255, 255, 0.1);
      color: white;
    }

    /* 自定义确认对话框样式 */
    .custom-confirm {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.7);
      z-index: 10001;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .confirm-content {
      background: white;
      border-radius: 8px;
      padding: 16px;
      max-width: 260px;
      width: 90%;
      text-align: center;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

    .confirm-icon {
      font-size: 24px;
      margin-bottom: 8px;
    }

    .confirm-message {
      color: #333;
      font-size: 13px;
      line-height: 1.4;
      margin-bottom: 16px;
      word-break: break-word;
    }

    .confirm-buttons {
      display: flex;
      gap: 8px;
      justify-content: center;
    }

    .confirm-btn {
      padding: 6px 16px;
      border: none;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.2s;
      min-width: 60px;
    }

    .confirm-cancel {
      background: #f5f5f5;
      color: #666;
    }

    .confirm-cancel:hover {
      background: #e0e0e0;
    }

    .confirm-ok {
      background: #F56C6C;
      color: white;
    }

    .confirm-ok:hover {
      background: #e55353;
    }

    /* 成功/错误/警告图标样式 */
    .toast-success {
      color: #67C23A;
    }

    .toast-error {
      color: #F56C6C;
    }

    .toast-warning {
      color: #E6A23C;
    }

    .toast-info {
      color: #409EFF;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="input-container">
      <input type="text" id="usecaseName" placeholder="请输入用例名称">
    </div>

    <button class="btn btn-start" id="startBtn" title="开始录制">
      <svg viewBox="0 0 24 24"><circle cx="12" cy="12" r="8"/></svg>
    </button>

    <button class="btn btn-pause hidden" id="pauseBtn" title="暂停录制">
      <svg viewBox="0 0 24 24"><rect x="6" y="4" width="4" height="16"/><rect x="14" y="4" width="4" height="16"/></svg>
    </button>

    <button class="btn btn-resume hidden" id="resumeBtn" title="继续录制">
      <svg viewBox="0 0 24 24"><polygon points="5,3 19,12 5,21"/></svg>
    </button>

    <button class="btn btn-stop hidden" id="stopBtn" title="停止录制">
      <svg viewBox="0 0 24 24"><rect x="6" y="6" width="12" height="12"/></svg>
    </button>

    <button class="btn btn-cancel hidden" id="cancelBtn" title="取消录制" style="background: #F56C6C;">
      <svg viewBox="0 0 24 24"><path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/></svg>
    </button>

    <button class="btn btn-screenshot" id="screenshotBtn" title="截图">
      <svg viewBox="0 0 24 24"><path d="M12 15.5A3.5 3.5 0 0 0 15.5 12A3.5 3.5 0 0 0 12 8.5A3.5 3.5 0 0 0 8.5 12A3.5 3.5 0 0 0 12 15.5M12 9A3 3 0 0 1 15 12A3 3 0 0 1 12 15A3 3 0 0 1 9 12A3 3 0 0 1 12 9M17 3H7C5.9 3 5 3.9 5 5V19C5 20.1 5.9 21 7 21H17C18.1 21 19 20.1 19 19V5C19 3.9 18.1 3 17 3M17 19H7V5H17V19Z"/></svg>
    </button>
  </div>

  <!-- 自定义提示组件 -->
  <div id="customToast" class="custom-toast hidden">
    <div class="toast-content">
      <div class="toast-icon" id="toastIcon"></div>
      <div class="toast-message" id="toastMessage"></div>
      <button class="toast-close" id="toastClose">&times;</button>
    </div>
  </div>

  <!-- 自定义确认对话框 -->
  <div id="customConfirm" class="custom-confirm hidden">
    <div class="confirm-content">
      <div class="confirm-icon">⚠️</div>
      <div class="confirm-message" id="confirmMessage"></div>
      <div class="confirm-buttons">
        <button class="confirm-btn confirm-cancel" id="confirmCancel">取消</button>
        <button class="confirm-btn confirm-ok" id="confirmOk">确定</button>
      </div>
    </div>
  </div>
</body>
</html>
    `
  }

  /**
   * 初始化悬浮窗功能
   */
  private initializeFloatingWindow(): void {
    if (!this.floatingWindow) return

    console.log('🚀 开始初始化悬浮窗功能...');

    const script = this.floatingWindow.document.createElement('script')
    script.textContent = this.getFloatingWindowScript()
    this.floatingWindow.document.head.appendChild(script)

    console.log('📜 悬浮窗脚本已注入');

    // 设置消息监听
    this.setupMessageListener()

    console.log('✅ 悬浮窗初始化完成');
  }

  /**
   * 设置消息监听
   */
  private setupMessageListener(): void {
    const messageHandler = (event: MessageEvent) => {
      if (event.source !== this.floatingWindow) return

      const { type, data } = event.data
      this.emit(type, data)
    }

    window.addEventListener('message', messageHandler)

    // 清理监听器
    if (this.floatingWindow) {
      this.floatingWindow.addEventListener('beforeunload', () => {
        window.removeEventListener('message', messageHandler)
        this.emit('close')
      })
    }
  }

  /**
   * 获取悬浮窗的JavaScript脚本
   */
  private getFloatingWindowScript(): string {
    return `
      console.log('🎬 悬浮窗脚本开始执行...');

      // 状态变量
      let isRecording = false;
      let isPaused = false;

      // 自定义提示函数
      function showToast(message, type = 'info', duration = 3000) {
        const toast = document.getElementById('customToast');
        const icon = document.getElementById('toastIcon');
        const messageEl = document.getElementById('toastMessage');
        const closeBtn = document.getElementById('toastClose');

        // 设置图标
        const icons = {
          success: '✅',
          error: '❌',
          warning: '⚠️',
          info: 'ℹ️'
        };
        icon.textContent = icons[type] || icons.info;
        icon.className = 'toast-icon toast-' + type;

        // 设置消息
        messageEl.textContent = message;

        // 显示提示
        toast.classList.remove('hidden');

        // 自动隐藏
        const hideToast = () => {
          toast.classList.add('hidden');
        };

        // 设置自动隐藏定时器
        const timer = setTimeout(hideToast, duration);

        // 点击关闭按钮
        closeBtn.onclick = () => {
          clearTimeout(timer);
          hideToast();
        };

        // 点击提示框外部关闭
        toast.onclick = (e) => {
          if (e.target === toast) {
            clearTimeout(timer);
            hideToast();
          }
        };
      }

      // 自定义确认对话框
      function showConfirm(message, onConfirm, onCancel) {
        const confirm = document.getElementById('customConfirm');
        const messageEl = document.getElementById('confirmMessage');
        const okBtn = document.getElementById('confirmOk');
        const cancelBtn = document.getElementById('confirmCancel');

        // 设置消息
        messageEl.textContent = message;

        // 显示确认框
        confirm.classList.remove('hidden');

        // 确定按钮事件
        okBtn.onclick = () => {
          confirm.classList.add('hidden');
          if (onConfirm) onConfirm();
        };

        // 取消按钮事件
        cancelBtn.onclick = () => {
          confirm.classList.add('hidden');
          if (onCancel) onCancel();
        };

        // 点击背景关闭
        confirm.onclick = (e) => {
          if (e.target === confirm) {
            confirm.classList.add('hidden');
            if (onCancel) onCancel();
          }
        };
      }

      // 获取DOM元素
      console.log('🔍 开始获取DOM元素...');
      const usecaseNameInput = document.getElementById('usecaseName');
      const startBtn = document.getElementById('startBtn');
      const pauseBtn = document.getElementById('pauseBtn');
      const resumeBtn = document.getElementById('resumeBtn');
      const stopBtn = document.getElementById('stopBtn');
      const cancelBtn = document.getElementById('cancelBtn');
      const screenshotBtn = document.getElementById('screenshotBtn');

      // 验证DOM元素是否正确获取
      console.log('📋 DOM元素检查:', {
        usecaseNameInput: !!usecaseNameInput,
        startBtn: !!startBtn,
        pauseBtn: !!pauseBtn,
        resumeBtn: !!resumeBtn,
        stopBtn: !!stopBtn,
        cancelBtn: !!cancelBtn,
        screenshotBtn: !!screenshotBtn
      });

      // 发送消息到父窗口
      function sendMessage(type, data) {
        if (window.opener) {
          window.opener.postMessage({ type, data }, '*');
        }
      }

      // 验证用例名称
      function validateUsecaseName() {
        const name = usecaseNameInput.value.trim();
        const isValid = name.length > 0;

        // 只有在非录制状态下才更新按钮状态
        if (!isRecording) {
          startBtn.disabled = !isValid;
        }

        return isValid;
      }

      // 更新UI状态
      function updateUI() {
        if (isRecording) {
          startBtn.classList.add('hidden');
          stopBtn.classList.remove('hidden');
          cancelBtn.classList.remove('hidden');

          if (isPaused) {
            pauseBtn.classList.add('hidden');
            resumeBtn.classList.remove('hidden');
          } else {
            pauseBtn.classList.remove('hidden');
            resumeBtn.classList.add('hidden');
          }
        } else {
          startBtn.classList.remove('hidden');
          pauseBtn.classList.add('hidden');
          resumeBtn.classList.add('hidden');
          stopBtn.classList.add('hidden');
          cancelBtn.classList.add('hidden');
          validateUsecaseName();
        }
      }

      // 获取认证token（与主应用保持一致）
      async function getAuthToken() {
        console.log('🔍 开始获取认证token...');

        // 尝试从主窗口获取当前用户的token
        if (window.opener && window.opener.getCurrentUserToken) {
          console.log('🪟 尝试从主窗口获取token...');
          try {
            const token = await window.opener.getCurrentUserToken();
            if (token) {
              console.log('✅ 从主窗口获取token成功');
              return token;
            } else {
              console.log('⚠️ 主窗口返回空token');
            }
          } catch (error) {
            console.error('❌ 从主窗口获取token失败:', error);
          }
        } else {
          console.log('⚠️ 主窗口不可用或未暴露getCurrentUserToken方法');
        }

        // 尝试从主窗口获取token管理器
        console.log('🔧 尝试从主窗口获取token管理器...');
        try {
          if (window.opener && window.opener.getTokenManager) {
            console.log('📦 从主窗口获取token管理器');
            const tokenManager = window.opener.getTokenManager();
            const tokenResult = await tokenManager.smartRefreshToken();
            console.log('🔄 token管理器响应:', tokenResult);

            if (tokenResult.success && tokenResult.accessToken) {
              console.log('✅ 从token管理器获取token成功');
              return tokenResult.accessToken;
            } else if (tokenResult.message?.includes('Token仍然有效')) {
              console.log('✅ token仍然有效，获取现有token');
              return tokenManager.getAccessToken();
            }
          } else {
            console.log('⚠️ 主窗口未暴露getTokenManager方法');
          }
        } catch (error) {
          console.error('❌ token管理器获取token失败:', error);
        }

        // 兼容性: 从localStorage获取
        console.log('💾 尝试从localStorage获取token...');
        let token = localStorage.getItem('auth_token') || localStorage.getItem('access_token');
        if (token) {
          console.log('✅ 从localStorage获取token成功');
          return token;
        }

        // 兼容性: 从sessionStorage获取
        console.log('💾 尝试从sessionStorage获取token...');
        token = sessionStorage.getItem('auth_token') || sessionStorage.getItem('access_token');
        if (token) {
          console.log('✅ 从sessionStorage获取token成功');
          return token;
        }

        console.error('❌ 所有方式都无法获取token');
        return null;
      }

      // 检查token是否过期（简单版本）
      function isTokenExpired(token) {
        if (!token) return true;

        try {
          const payload = JSON.parse(atob(token.split('.')[1]));
          if (!payload.exp) return false; // 如果没有过期时间，假设有效

          return Date.now() >= payload.exp * 1000;
        } catch (error) {
          return true;
        }
      }

      // 发送API请求（支持自动token刷新）
      async function sendApiRequest(url, method = 'POST', data = null) {
        console.log('🔐 开始获取认证token...');
        const token = await getAuthToken();
        console.log('🔑 获取到token:', token ? '✅ 有效' : '❌ 无效');

        // 检查token有效性
        if (!token) {
          console.error('❌ 未找到认证token');
          if (window.opener) {
            window.opener.postMessage({
              type: 'authError',
              data: { message: '未找到认证信息，请先登录' }
            }, '*');
          }

          return { success: false, message: '未找到认证信息，请先登录' };
        }

        // 构建完整的API URL
        const API_BASE_URL = window.location.origin;
        const fullUrl = url.startsWith('http') ? url : API_BASE_URL + '/api' + url;

        const headers = {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ' + token,
          'X-Request-Source': 'FloatingWindow',
          'X-Request-ID': 'fw_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
        };

        const options = { method, headers };
        if (data) {
          options.body = JSON.stringify(data);
        }

        try {
          const requestInfo = { url: fullUrl, method, data, headers: { ...headers, Authorization: 'Bearer ***' } };
          console.log('悬浮窗API请求:', requestInfo);

          // 将请求信息发送到主窗口的开发者工具
          sendMessage('networkRequest', {
            type: 'request',
            timestamp: Date.now(),
            url: fullUrl,
            method,
            data,
            headers: { ...headers, Authorization: 'Bearer [HIDDEN]' }
          });

          const response = await fetch(fullUrl, options);

          // 检查响应状态
          if (!response.ok) {
            let errorMessage = '未知错误';
            try {
              const errorData = await response.json();
              errorMessage = errorData.message || errorData.detail || errorMessage;
            } catch (e) {
              // 如果无法解析错误响应，使用默认错误信息
            }

            if (response.status === 401) {
              if (window.opener) {
                window.opener.postMessage({
                  type: 'authError',
                  data: { message: '认证失败，请重新登录' }
                }, '*');
              }
              return { success: false, message: '认证失败，请重新登录' };
            } else if (response.status === 403) {
              return { success: false, message: '没有权限执行此操作' };
            } else if (response.status === 404) {
              return { success: false, message: '请求的接口不存在: ' + fullUrl };
            } else if (response.status === 422) {
              return { success: false, message: '参数验证失败: ' + errorMessage };
            } else {
              return { success: false, message: '服务器错误 (' + response.status + '): ' + errorMessage };
            }
          }

          const result = await response.json();
          console.log('悬浮窗API响应:', result);

          // 将响应信息发送到主窗口的开发者工具
          sendMessage('networkRequest', {
            type: 'response',
            timestamp: Date.now(),
            url: fullUrl,
            status: response.status,
            statusText: response.statusText,
            data: result
          });

          return result;
        } catch (error) {
          console.error('悬浮窗API请求异常:', error);

          // 将错误信息发送到主窗口
          sendMessage('networkRequest', {
            type: 'error',
            timestamp: Date.now(),
            url: fullUrl,
            error: error.message
          });

          return { success: false, message: '网络请求失败: ' + error.message };
        }
      }

      // 开始录制事件
      startBtn.addEventListener('click', async () => {
        console.log('🎬 开始录制按钮被点击');

        // 注意: 根据新的API规范，开始录制不需要用例名称参数
        // 用例名称将在停止录制时提供

        try {
          console.log('🔄 准备调用录制API...');
          // 调用新的操作录制API - 开始录制（无需参数）
          const result = await sendApiRequest('/api/v1/operation-recording/start', 'POST');
          console.log('📡 录制API响应:', result);

          // 检查后端响应状态（后端返回 status: 'success' 或 'error'）
          if (result.status === 'success') {
            isRecording = true;
            updateUI();
            sendMessage('startRecord');
            // 通知主窗口显示成功消息
            sendMessage('recordingStatus', {
              type: 'success',
              action: 'start',
              message: result.message || '开始录制成功',
              data: result
            });
          } else {
            const errorMsg = result.message || '未知错误';
            showToast('开始录制失败: ' + errorMsg, 'error');
            // 通知主窗口显示错误消息
            sendMessage('recordingStatus', {
              type: 'error',
              action: 'start',
              message: '开始录制失败: ' + errorMsg
            });
          }
        } catch (error) {
          const errorMsg = error.message || '网络错误';
          showToast('开始录制失败: ' + errorMsg, 'error');
          sendMessage('recordingStatus', {
            type: 'error',
            action: 'start',
            message: '开始录制失败: ' + errorMsg
          });
        }
      });

      // 暂停录制事件
      pauseBtn.addEventListener('click', async () => {
        try {
          const result = await sendApiRequest('/api/v1/operation-recording/pause');

          if (result.status === 'success') {
            isPaused = true;
            updateUI();
            sendMessage('pauseRecord');
            sendMessage('recordingStatus', {
              type: 'success',
              action: 'pause',
              message: result.message || '暂停录制成功',
              data: result
            });
          } else {
            const errorMsg = result.message || '未知错误';
            showToast('暂停录制失败: ' + errorMsg, 'error');
            sendMessage('recordingStatus', {
              type: 'error',
              action: 'pause',
              message: '暂停录制失败: ' + errorMsg
            });
          }
        } catch (error) {
          const errorMsg = error.message || '网络错误';
          showToast('暂停录制失败: ' + errorMsg, 'error');
          sendMessage('recordingStatus', {
            type: 'error',
            action: 'pause',
            message: '暂停录制失败: ' + errorMsg
          });
        }
      });

      // 继续录制事件
      resumeBtn.addEventListener('click', async () => {
        try {
          const result = await sendApiRequest('/api/v1/operation-recording/resume');

          if (result.status === 'success') {
            isPaused = false;
            updateUI();
            sendMessage('resumeRecord');
            sendMessage('recordingStatus', {
              type: 'success',
              action: 'resume',
              message: result.message || '继续录制成功',
              data: result
            });
          } else {
            const errorMsg = result.message || '未知错误';
            showToast('继续录制失败: ' + errorMsg, 'error');
            sendMessage('recordingStatus', {
              type: 'error',
              action: 'resume',
              message: '继续录制失败: ' + errorMsg
            });
          }
        } catch (error) {
          const errorMsg = error.message || '网络错误';
          showToast('继续录制失败: ' + errorMsg, 'error');
          sendMessage('recordingStatus', {
            type: 'error',
            action: 'resume',
            message: '继续录制失败: ' + errorMsg
          });
        }
      });

      // 停止录制事件
      stopBtn.addEventListener('click', async () => {
        // 停止录制需要提供用例名称
        if (!validateUsecaseName()) {
          showToast('请输入用例名称', 'warning');
          return;
        }

        const usecaseName = usecaseNameInput.value.trim();

        // 乐观更新: 立即更新UI状态，不等待API响应
        console.log('🔄 执行乐观更新 - 立即停止录制状态');
        isRecording = false;
        isPaused = false;
        updateUI();

        // 立即通知主界面更新状态
        sendMessage('stopRecord', { usecaseName, filePath: null });

        // 不在悬浮窗显示提示，只通知主界面
        // showToast('正在停止录制...', 'info');

        // 异步处理API请求
        (async () => {
          try {
            console.log('📡 发送停止录制API请求...');
            const result = await sendApiRequest('/api/v1/operation-recording/stop', 'POST', {
              case_name: usecaseName
            });

            if (result.status === 'success') {
              console.log('✅ 停止录制API成功');
              // 不在悬浮窗显示成功提示，只通知主界面
              let successMsg = '停止录制成功';
              if (result.data?.file_path) {
                successMsg = 'Recording stopped, file saved to: ' + result.data.file_path;
              } else if (result.message) {
                successMsg = result.message;
              }
              // showToast(successMsg, 'success');

              // 通知主界面显示成功消息
              sendMessage('recordingStatus', {
                type: 'success',
                action: 'stop',
                message: successMsg,
                data: result
              });

              // 更新停止录制事件，包含文件路径
              sendMessage('stopRecord', { usecaseName, filePath: result.data?.file_path });
            } else {
              console.error('❌ 停止录制API失败:', result);
              const errorMsg = result.message || '停止录制失败';
              // 不在悬浮窗显示错误提示，只通知主界面
              // showToast(errorMsg, 'error');

              // 通知主界面显示错误消息（但不回滚UI状态）
              sendMessage('recordingStatus', {
                type: 'error',
                action: 'stop',
                message: errorMsg
              });
            }
          } catch (error) {
            console.error('❌ 停止录制API异常:', error);
            const errorMsg = error.message || '网络错误';
            // 不在悬浮窗显示错误提示，只通知主界面
            // showToast('停止录制失败: ' + errorMsg, 'error');

            // 通知主界面显示错误消息（但不回滚UI状态）
            sendMessage('recordingStatus', {
              type: 'error',
              action: 'stop',
              message: '停止录制失败: ' + errorMsg
            });
          }
        })();
      });

      // 取消录制事件
      cancelBtn.addEventListener('click', async () => {
        showConfirm(
          '确定要取消录制吗？所有录制数据将被丢弃。',
          async () => {
            try {
              const result = await sendApiRequest('/api/v1/operation-recording/cancel');

              if (result.status === 'success') {
                isRecording = false;
                isPaused = false;
                updateUI();
                sendMessage('cancelRecord');
                sendMessage('recordingStatus', {
                  type: 'success',
                  action: 'cancel',
                  message: result.message || '取消录制成功',
                  data: result
                });
                // 不在悬浮窗显示提示，只通知主界面
                // showToast('录制已取消', 'info');
              } else {
                const errorMsg = result.message || '未知错误';
                // 不在悬浮窗显示错误提示，只通知主界面
                // showToast('取消录制失败: ' + errorMsg, 'error');
                sendMessage('recordingStatus', {
                  type: 'error',
                  action: 'cancel',
                  message: '取消录制失败: ' + errorMsg
                });
              }
            } catch (error) {
              const errorMsg = error.message || 'Network Error';
              // 不在悬浮窗显示错误提示，只通知主界面
              // showToast('取消录制失败: ' + errorMsg, 'error');
              sendMessage('recordingStatus', {
                type: 'error',
                action: 'cancel',
                message: '取消录制失败: ' + errorMsg
              });
            }
          }
        );
      });

      // 截图事件
      screenshotBtn.addEventListener('click', () => {
        sendMessage('screenshot');
      });

      // 输入框事件
      usecaseNameInput.addEventListener('input', validateUsecaseName);
      usecaseNameInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter' && validateUsecaseName() && !isRecording) {
          startBtn.click();
        }
      });

      // 添加右键菜单支持（用于打开开发者工具）
      document.addEventListener('contextmenu', (e) => {
        // 允许右键菜单，方便调试
        // 用户可以右键 → 检查元素 来打开悬浮窗的开发者工具
      });

      // 添加快捷键支持
      document.addEventListener('keydown', (e) => {
        // F12 或 Ctrl+Shift+I 打开开发者工具提示
        if (e.key === 'F12' || (e.ctrlKey && e.shiftKey && e.key === 'I')) {
          console.log('💡 提示: 在悬浮窗中按F12或右键→检查元素可查看网络请求');
        }

        // Ctrl+Shift+D 显示调试信息
        if (e.ctrlKey && e.shiftKey && e.key === 'D') {
          console.log('🔍 悬浮窗调试信息:', {
            '窗口状态': {
              isRecording,
              isPaused,
              '窗口大小': { width: window.innerWidth, height: window.innerHeight },
              '窗口位置': { x: window.screenX, y: window.screenY }
            },
            'DOM元素': {
              usecaseNameInput: !!usecaseNameInput,
              startBtn: !!startBtn,
              pauseBtn: !!pauseBtn,
              resumeBtn: !!resumeBtn,
              stopBtn: !!stopBtn,
              cancelBtn: !!cancelBtn,
              screenshotBtn: !!screenshotBtn
            },
            '主窗口连接': !!window.opener
          });
        }
      });

      // 初始化
      console.log('🔧 开始初始化悬浮窗UI...');
      validateUsecaseName();
      updateUI();
      console.log('✅ 悬浮窗脚本初始化完成，事件监听器已绑定');
      console.log('💡 调试提示: 在悬浮窗中按F12查看网络请求，按Ctrl+Shift+D查看调试信息');

      // 发送初始化完成消息
      sendMessage('floatingWindowReady', { timestamp: Date.now() });
    `
  }
}

// 创建单例实例
export const floatingWindowManager = new FloatingWindowManager()
